// Copyright Epic Games, Inc. All Rights Reserved.

#include "BlackTideWeaponBase.h"
#include "../BlackTideCharacter.h"
#include "../Components/BlackTideInteractableComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/BoxComponent.h"
#include "Components/SceneComponent.h"
#include "Engine/World.h"
#include "Animation/AnimMontage.h"
#include "DrawDebugHelpers.h"

DEFINE_LOG_CATEGORY_STATIC(LogBlackTideWeapon, Log, All);

ABlackTideWeaponBase::ABlackTideWeaponBase()
{
	PrimaryActorTick.bCanEverTick = false;

	// Create root component
	RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootComponent"));

	// Create attach point (where character grabs the weapon)
	AttachPoint = CreateDefaultSubobject<USceneComponent>(TEXT("AttachPoint"));
	AttachPoint->SetupAttachment(RootComponent);

	// Create weapon mesh
	WeaponMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("WeaponMesh"));
	WeaponMesh->SetupAttachment(AttachPoint);
	WeaponMesh->SetCollisionEnabled(ECollisionEnabled::NoCollision);
	WeaponMesh->SetCastShadow(true);

	// Create hit collision (disabled by default)
	HitCollision = CreateDefaultSubobject<UBoxComponent>(TEXT("HitCollision"));
	HitCollision->SetupAttachment(WeaponMesh);
	HitCollision->SetCollisionEnabled(ECollisionEnabled::NoCollision);
	HitCollision->SetCollisionObjectType(ECC_WorldDynamic);
	HitCollision->SetCollisionResponseToAllChannels(ECR_Ignore);
	HitCollision->SetCollisionResponseToChannel(ECC_WorldStatic, ECR_Overlap);    // OVERLAP for resource nodes
	HitCollision->SetCollisionResponseToChannel(ECC_WorldDynamic, ECR_Overlap);   // OVERLAP for objects
	HitCollision->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);           // OVERLAP for characters

	// Set VERY large collision box for testing
	HitCollision->SetBoxExtent(FVector(200.0f, 200.0f, 200.0f)); // MASSIVE for debugging
	UE_LOG(LogBlackTideWeapon, Warning, TEXT("🔧 WEAPON: Set collision to OVERLAP mode with 200x200x200 box"));

	// Set default values
	WeaponCategory = EWeaponCategory::Melee;
	WeaponID = TEXT("DefaultWeapon");
	WeaponName = TEXT("Default Weapon");
	
	// Default damage data
	DamageData.BaseDamage = 10.0f;
	DamageData.ResourceDamage = 15.0f;
	DamageData.CriticalMultiplier = 2.0f;
	DamageData.AttackSpeed = 1.0f;
	DamageData.AttackRange = 150.0f;

	// Initialize state
	CurrentWielder = nullptr;
	LastHitTime = 0.0f;
	bHitDetectionEnabled = false;

	// Attack behavior settings
	bEndAttackOnResourceHit = true;
	bEndAttackOnCombatHit = false;
	MaxAttackDuration = 1.0f;

	// Debug settings
	bShowCollisionDebug = true;
	DebugDrawTime = 2.0f;
}

void ABlackTideWeaponBase::BeginPlay()
{
	Super::BeginPlay();

	// Bind hit collision event - use OnComponentHit for better collision detection
	if (HitCollision)
	{
		HitCollision->OnComponentHit.AddDynamic(this, &ABlackTideWeaponBase::OnHitCollisionHit);
		// Also bind overlap as backup
		HitCollision->OnComponentBeginOverlap.AddDynamic(this, &ABlackTideWeaponBase::OnHitCollisionBeginOverlap);
	}

	UE_LOG(LogBlackTideWeapon, Log, TEXT("Weapon %s (%s) initialized"), *WeaponName, *WeaponID);
}

void ABlackTideWeaponBase::OnHitCollisionHit(UPrimitiveComponent* HitComp, AActor* OtherActor,
	UPrimitiveComponent* OtherComp, FVector NormalImpulse, const FHitResult& Hit)
{
	UE_LOG(LogBlackTideWeapon, Warning, TEXT("🎯 HIT EVENT: %s hit %s"),
		*WeaponName, OtherActor ? *OtherActor->GetName() : TEXT("NULL"));

	// Process hit using the same logic as overlap
	ProcessHitEvent(OtherActor, Hit);
}

void ABlackTideWeaponBase::StartAttack(ABlackTideCharacter* Wielder)
{
	if (!Wielder)
	{
		UE_LOG(LogBlackTideWeapon, Warning, TEXT("Cannot start attack - no wielder"));
		return;
	}

	CurrentWielder = Wielder;
	HitActorsThisSwing.Empty();

	UE_LOG(LogBlackTideWeapon, Warning, TEXT("🗡️ %s started attack with %s"),
		*Wielder->GetName(), *WeaponName);

	// Enable hit detection for the entire swing
	EnableHitDetection();

	// Set timer to disable hit detection after swing duration
	FTimerHandle DisableHitTimer;
	GetWorld()->GetTimerManager().SetTimer(DisableHitTimer, [this]()
	{
		UE_LOG(LogBlackTideWeapon, Warning, TEXT("⏰ Attack duration expired, ending attack"));
		EndAttack();
	}, MaxAttackDuration, false);
}

void ABlackTideWeaponBase::EndAttack()
{
	UE_LOG(LogBlackTideWeapon, Warning, TEXT("🛑 %s ended attack"), *WeaponName);

	// Disable hit detection
	DisableHitDetection();

	// Clear state
	HitActorsThisSwing.Empty();
	CurrentWielder = nullptr;
}

void ABlackTideWeaponBase::EnableHitDetection()
{
	if (HitCollision)
	{
		HitCollision->SetCollisionEnabled(ECollisionEnabled::QueryOnly); // QueryOnly for overlaps
		bHitDetectionEnabled = true;
		UE_LOG(LogBlackTideWeapon, Warning, TEXT("✅ Hit detection enabled for %s (QueryOnly mode)"), *WeaponName);

		// Debug visualization
		if (bShowCollisionDebug)
		{
			FVector BoxExtent = HitCollision->GetScaledBoxExtent();
			FVector BoxLocation = HitCollision->GetComponentLocation();
			FRotator BoxRotation = HitCollision->GetComponentRotation();

			DrawDebugBox(GetWorld(), BoxLocation, BoxExtent, BoxRotation.Quaternion(),
				FColor::Green, false, DebugDrawTime, 0, 3.0f);

			UE_LOG(LogBlackTideWeapon, Warning, TEXT("🔍 DEBUG: LARGE Collision box at %s, extent %s"),
				*BoxLocation.ToString(), *BoxExtent.ToString());

			// Also log weapon and character positions
			UE_LOG(LogBlackTideWeapon, Warning, TEXT("🔍 DEBUG: Weapon at %s, Character at %s"),
				*GetActorLocation().ToString(),
				CurrentWielder ? *CurrentWielder->GetActorLocation().ToString() : TEXT("No Wielder"));
		}
	}
}

void ABlackTideWeaponBase::DisableHitDetection()
{
	if (HitCollision)
	{
		HitCollision->SetCollisionEnabled(ECollisionEnabled::NoCollision);
		bHitDetectionEnabled = false;
		UE_LOG(LogBlackTideWeapon, Warning, TEXT("🛑 Hit detection disabled for %s"), *WeaponName);

		// Debug visualization for disabled state
		if (bShowCollisionDebug)
		{
			FVector BoxExtent = HitCollision->GetScaledBoxExtent();
			FVector BoxLocation = HitCollision->GetComponentLocation();
			FRotator BoxRotation = HitCollision->GetComponentRotation();

			DrawDebugBox(GetWorld(), BoxLocation, BoxExtent, BoxRotation.Quaternion(),
				FColor::Red, false, DebugDrawTime * 0.5f, 0, 1.0f);
		}
	}
}

bool ABlackTideWeaponBase::IsHitDetectionEnabled() const
{
	return bHitDetectionEnabled && HitCollision && 
		   HitCollision->GetCollisionEnabled() != ECollisionEnabled::NoCollision;
}

UAnimMontage* ABlackTideWeaponBase::GetAttackMontage() const
{
	if (AttackMontage.IsNull())
	{
		return nullptr;
	}

	// Try to get already loaded asset
	UAnimMontage* Montage = AttackMontage.Get();
	if (Montage)
	{
		return Montage;
	}

	// Force load if not already loaded
	Montage = AttackMontage.LoadSynchronous();
	if (Montage)
	{
		UE_LOG(LogBlackTideWeapon, Log, TEXT("Loaded attack montage for %s"), *WeaponName);
	}
	else
	{
		UE_LOG(LogBlackTideWeapon, Warning, TEXT("Failed to load attack montage for %s"), *WeaponName);
	}

	return Montage;
}

bool ABlackTideWeaponBase::CanHarvestResource(const FString& ResourceType) const
{
	return CompatibleResourceTypes.Contains(ResourceType);
}

void ABlackTideWeaponBase::OnHitCollisionBeginOverlap(UPrimitiveComponent* OverlappedComponent,
	AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex,
	bool bFromSweep, const FHitResult& SweepResult)
{
	UE_LOG(LogBlackTideWeapon, Warning, TEXT("🔍 OVERLAP DETECTED: %s overlapped with %s"),
		*WeaponName, OtherActor ? *OtherActor->GetName() : TEXT("NULL"));

	// Ignore if hit detection is disabled
	if (!bHitDetectionEnabled)
	{
		UE_LOG(LogBlackTideWeapon, Warning, TEXT("❌ Hit detection disabled, ignoring overlap"));
		return;
	}

	// Ignore self hits
	if (!OtherActor || OtherActor == this || OtherActor == CurrentWielder)
	{
		UE_LOG(LogBlackTideWeapon, Warning, TEXT("❌ Self hit ignored"));
		return;
	}

	// Prevent multiple hits on same actor per swing
	if (HitActorsThisSwing.Contains(OtherActor))
	{
		UE_LOG(LogBlackTideWeapon, Warning, TEXT("❌ Already hit %s this swing"), *OtherActor->GetName());
		return;
	}

	// Check if we can hit this actor
	if (!CanHitActor(OtherActor))
	{
		UE_LOG(LogBlackTideWeapon, Warning, TEXT("❌ Cannot hit %s"), *OtherActor->GetName());
		return;
	}

	UE_LOG(LogBlackTideWeapon, Warning, TEXT("✅ Valid hit target: %s"), *OtherActor->GetName());

	// Process hit using shared logic
	ProcessHitEvent(OtherActor, SweepResult);
}

void ABlackTideWeaponBase::ProcessHitEvent(AActor* OtherActor, const FHitResult& HitResult)
{
	if (!OtherActor)
	{
		return;
	}

	// Record hit
	HitActorsThisSwing.Add(OtherActor);
	LastHitTime = GetWorld()->GetTimeSeconds();

	UE_LOG(LogBlackTideWeapon, Warning, TEXT("⚔️ WEAPON HIT: %s hit %s"),
		*WeaponName, *OtherActor->GetName());

	// Debug visualization for hit
	if (bShowCollisionDebug)
	{
		FVector HitLocation = HitResult.Location;
		if (HitLocation.IsZero())
		{
			HitLocation = OtherActor->GetActorLocation();
		}

		// Draw hit point
		DrawDebugSphere(GetWorld(), HitLocation, 10.0f, 12, FColor::Yellow, false, DebugDrawTime);

		// Draw line from weapon to hit point
		FVector WeaponLocation = HitCollision->GetComponentLocation();
		DrawDebugLine(GetWorld(), WeaponLocation, HitLocation, FColor::Orange, false, DebugDrawTime, 0, 3.0f);

		UE_LOG(LogBlackTideWeapon, Warning, TEXT("🎯 DEBUG HIT: Location %s, Actor %s"),
			*HitLocation.ToString(), *OtherActor->GetName());
	}

	// Process the hit
	ProcessHit(OtherActor, HitResult);
}

void ABlackTideWeaponBase::ProcessHit(AActor* HitActor, const FHitResult& Hit)
{
	if (!HitActor || !CurrentWielder)
	{
		return;
	}

	// Determine hit type and process accordingly
	if (IsResourceNode(HitActor))
	{
		ProcessResourceHit(HitActor, Hit);
	}
	else
	{
		ProcessCombatHit(HitActor, Hit);
	}

	// Broadcast general hit event
	float Damage = CalculateDamage(HitActor);
	OnWeaponHitTarget.Broadcast(HitActor, Hit.Location, Damage);
}

void ABlackTideWeaponBase::ProcessResourceHit(AActor* ResourceActor, const FHitResult& Hit)
{
	UBlackTideInteractableComponent* Interactable = ResourceActor->FindComponentByClass<UBlackTideInteractableComponent>();
	if (!Interactable)
	{
		UE_LOG(LogBlackTideWeapon, Warning, TEXT("Resource actor %s has no interactable component"),
			*ResourceActor->GetName());
		return;
	}

	// Check if we have the right tool for this resource
	if (!Interactable->CanInteract(CurrentWielder))
	{
		UE_LOG(LogBlackTideWeapon, Warning, TEXT("Cannot harvest %s - incompatible tool or requirements"),
			*ResourceActor->GetName());
		return;
	}

	// Start resource interaction (the resource node defines what drops, not the weapon)
	bool bStarted = Interactable->StartInteraction(CurrentWielder);
	if (bStarted)
	{
		UE_LOG(LogBlackTideWeapon, Warning, TEXT("✅ Started harvesting %s with %s"),
			*ResourceActor->GetName(), *WeaponName);

		// The resource node will handle what items are given to the player
		// We just broadcast that a resource was hit
		FInventoryItem DummyItem; // Empty item, actual items come from resource node
		OnWeaponResourceHit.Broadcast(ResourceActor, DummyItem);

		// End attack immediately after successful resource hit (like chopping a tree)
		if (bEndAttackOnResourceHit)
		{
			UE_LOG(LogBlackTideWeapon, Warning, TEXT("🎯 Resource hit successful, ending attack"));
			EndAttack();
		}
	}
	else
	{
		UE_LOG(LogBlackTideWeapon, Warning, TEXT("❌ Failed to start harvesting %s - check tool requirements"),
			*ResourceActor->GetName());
	}
}

void ABlackTideWeaponBase::ProcessCombatHit(AActor* TargetActor, const FHitResult& Hit)
{
	float Damage = CalculateDamage(TargetActor);

	UE_LOG(LogBlackTideWeapon, Warning, TEXT("💥 Combat hit on %s for %.1f damage"),
		*TargetActor->GetName(), Damage);

	// TODO: Apply damage to target actor
	// This would integrate with a health/damage system when implemented

	// Optionally end attack after combat hit (configurable per weapon)
	if (bEndAttackOnCombatHit)
	{
		UE_LOG(LogBlackTideWeapon, Warning, TEXT("⚔️ Combat hit successful, ending attack"));
		EndAttack();
	}
}

bool ABlackTideWeaponBase::CanHitActor(AActor* Actor) const
{
	if (!Actor || !CurrentWielder)
	{
		return false;
	}

	// Don't hit ourselves or our wielder
	if (Actor == this || Actor == CurrentWielder)
	{
		return false;
	}

	// Add more validation as needed (teams, invulnerability, etc.)
	return true;
}

float ABlackTideWeaponBase::CalculateDamage(AActor* Target) const
{
	float Damage = DamageData.BaseDamage;

	// Use resource damage for resource nodes
	if (IsResourceNode(Target))
	{
		Damage = DamageData.ResourceDamage;
	}

	// Add more damage calculations as needed (critical hits, resistances, etc.)
	return Damage;
}

bool ABlackTideWeaponBase::IsResourceNode(AActor* Actor) const
{
	if (!Actor)
	{
		return false;
	}

	// Check if actor has an interactable component (resource nodes should have this)
	return Actor->FindComponentByClass<UBlackTideInteractableComponent>() != nullptr;
}

void ABlackTideWeaponBase::ToggleCollisionDebug()
{
	bShowCollisionDebug = !bShowCollisionDebug;
	UE_LOG(LogBlackTideWeapon, Warning, TEXT("🔍 Collision debug %s for %s"),
		bShowCollisionDebug ? TEXT("ENABLED") : TEXT("DISABLED"), *WeaponName);
}

void ABlackTideWeaponBase::SetCollisionDebug(bool bEnabled)
{
	bShowCollisionDebug = bEnabled;
	UE_LOG(LogBlackTideWeapon, Warning, TEXT("🔍 Collision debug set to %s for %s"),
		bEnabled ? TEXT("ENABLED") : TEXT("DISABLED"), *WeaponName);
}
