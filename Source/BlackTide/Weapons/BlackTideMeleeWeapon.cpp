// Copyright Epic Games, Inc. All Rights Reserved.

#include "BlackTideMeleeWeapon.h"
#include "../BlackTideCharacter.h"
#include "Engine/World.h"
#include "Animation/AnimMontage.h"
#include "TimerManager.h"

DEFINE_LOG_CATEGORY_STATIC(LogBlackTideMeleeWeapon, Log, All);

ABlackTideMeleeWeapon::ABlackTideMeleeWeapon()
{
	// Set weapon category
	WeaponCategory = EWeaponCategory::Melee;

	// Default melee data
	MeleeData.MeleeType = EMeleeWeaponType::Axe;
	MeleeData.SwingArc = 180.0f; // Wide arc for resource gathering (90° each side)
	MeleeData.ComboWindow = 0.5f;
	MeleeData.MaxComboCount = 3;
	MeleeData.bCanBlock = false;
	MeleeData.BlockDamageReduction = 0.5f;

	// Initialize combo state
	CurrentComboCount = 0;
	LastAttackTime = 0.0f;
	bIsBlocking = false;
}

void ABlackTideMeleeWeapon::BeginPlay()
{
	Super::BeginPlay();

	UE_LOG(LogBlackTideMeleeWeapon, Log, TEXT("Melee weapon %s (%s) initialized"), 
		*WeaponName, *UEnum::GetValueAsString(MeleeData.MeleeType));
}

void ABlackTideMeleeWeapon::StartAttack(ABlackTideCharacter* Wielder)
{
	if (!Wielder)
	{
		UE_LOG(LogBlackTideMeleeWeapon, Warning, TEXT("Cannot start melee attack - no wielder"));
		return;
	}

	float CurrentTime = GetWorld()->GetTimeSeconds();

	// Check if we can start a combo
	if (CanStartCombo() && CurrentComboCount > 0)
	{
		StartComboAttack(CurrentComboCount);
	}
	else
	{
		// Start new attack chain
		CurrentComboCount = 1;
		LastAttackTime = CurrentTime;

		// Call parent implementation
		Super::StartAttack(Wielder);

		UE_LOG(LogBlackTideMeleeWeapon, Warning, TEXT("🗡️ Started melee attack (combo %d/%d)"), 
			CurrentComboCount, MeleeData.MaxComboCount);
	}

	// Set timer to reset combo if no follow-up attack
	FTimerHandle ComboResetTimer;
	GetWorld()->GetTimerManager().SetTimer(ComboResetTimer, this, &ABlackTideMeleeWeapon::ResetCombo, 
		MeleeData.ComboWindow, false);
}

void ABlackTideMeleeWeapon::EndAttack()
{
	Super::EndAttack();

	UE_LOG(LogBlackTideMeleeWeapon, Warning, TEXT("🛑 Ended melee attack"));
}

void ABlackTideMeleeWeapon::StartComboAttack(int32 ComboIndex)
{
	if (ComboIndex <= 0 || ComboIndex > MeleeData.MaxComboCount)
	{
		UE_LOG(LogBlackTideMeleeWeapon, Warning, TEXT("Invalid combo index: %d"), ComboIndex);
		return;
	}

	CurrentComboCount = ComboIndex;
	LastAttackTime = GetWorld()->GetTimeSeconds();

	// Enable hit detection for combo
	EnableHitDetection();

	UE_LOG(LogBlackTideMeleeWeapon, Warning, TEXT("⚔️ Combo attack %d/%d"), 
		CurrentComboCount, MeleeData.MaxComboCount);

	// Set timer to disable hit detection
	FTimerHandle DisableHitTimer;
	GetWorld()->GetTimerManager().SetTimer(DisableHitTimer, [this]()
	{
		DisableHitDetection();
	}, 0.8f, false); // Shorter window for combos
}

void ABlackTideMeleeWeapon::StartBlock()
{
	if (!MeleeData.bCanBlock)
	{
		UE_LOG(LogBlackTideMeleeWeapon, Warning, TEXT("Weapon %s cannot block"), *WeaponName);
		return;
	}

	bIsBlocking = true;
	UE_LOG(LogBlackTideMeleeWeapon, Warning, TEXT("🛡️ Started blocking with %s"), *WeaponName);

	// TODO: Implement blocking logic (damage reduction, stamina drain, etc.)
}

void ABlackTideMeleeWeapon::EndBlock()
{
	if (!bIsBlocking)
	{
		return;
	}

	bIsBlocking = false;
	UE_LOG(LogBlackTideMeleeWeapon, Warning, TEXT("🛡️ Stopped blocking"));
}

bool ABlackTideMeleeWeapon::CanStartCombo() const
{
	if (CurrentComboCount <= 0 || CurrentComboCount >= MeleeData.MaxComboCount)
	{
		return false;
	}

	float CurrentTime = GetWorld()->GetTimeSeconds();
	float TimeSinceLastAttack = CurrentTime - LastAttackTime;

	return TimeSinceLastAttack <= MeleeData.ComboWindow;
}

void ABlackTideMeleeWeapon::ProcessHit(AActor* HitActor, const FHitResult& Hit)
{
	// Use actor location if hit location is invalid (common with overlaps)
	FVector HitLocation = Hit.Location;
	if (HitLocation.IsZero() && HitActor)
	{
		HitLocation = HitActor->GetActorLocation();
		UE_LOG(LogBlackTideMeleeWeapon, Warning, TEXT("🔧 Using actor location instead of hit location"));
	}

	// Check if hit is within swing arc
	if (!IsInSwingArc(HitLocation))
	{
		UE_LOG(LogBlackTideMeleeWeapon, Warning, TEXT("Hit outside swing arc, ignoring"));
		return;
	}

	// Call parent implementation with combo multiplier
	Super::ProcessHit(HitActor, Hit);

	UE_LOG(LogBlackTideMeleeWeapon, Warning, TEXT("⚔️ Melee hit processed (combo %d, multiplier %.1fx)"),
		CurrentComboCount, GetComboMultiplier());
}

void ABlackTideMeleeWeapon::ProcessResourceHit(AActor* ResourceActor, const FHitResult& Hit)
{
	// Melee weapons are great for resource gathering
	Super::ProcessResourceHit(ResourceActor, Hit);

	// Add melee-specific resource logic if needed
	UE_LOG(LogBlackTideMeleeWeapon, Warning, TEXT("🌳 Melee resource hit on %s"), 
		*ResourceActor->GetName());
}

void ABlackTideMeleeWeapon::ProcessCombatHit(AActor* TargetActor, const FHitResult& Hit)
{
	// Apply combo multiplier to combat damage
	float BaseDamage = CalculateDamage(TargetActor);
	float ComboMultiplier = GetComboMultiplier();
	float FinalDamage = BaseDamage * ComboMultiplier;

	UE_LOG(LogBlackTideMeleeWeapon, Warning, TEXT("💥 Melee combat hit: %.1f base * %.1fx combo = %.1f damage"), 
		BaseDamage, ComboMultiplier, FinalDamage);

	// TODO: Apply actual damage to target
}

bool ABlackTideMeleeWeapon::IsInSwingArc(const FVector& HitLocation) const
{
	if (!CurrentWielder)
	{
		return true; // No wielder, allow all hits
	}

	// Calculate angle between wielder forward and hit direction
	FVector WielderLocation = CurrentWielder->GetActorLocation();
	FVector WielderForward = CurrentWielder->GetActorForwardVector();
	FVector HitDirection = (HitLocation - WielderLocation).GetSafeNormal();

	float DotProduct = FVector::DotProduct(WielderForward, HitDirection);
	float AngleRadians = FMath::Acos(DotProduct);
	float AngleDegrees = FMath::RadiansToDegrees(AngleRadians);

	// Check if within swing arc
	float HalfArc = MeleeData.SwingArc * 0.5f;
	bool bInArc = AngleDegrees <= HalfArc;

	// Debug logging for swing arc
	UE_LOG(LogBlackTideMeleeWeapon, Warning, TEXT("🎯 SWING ARC DEBUG: Angle=%.1f°, HalfArc=%.1f°, InArc=%s"),
		AngleDegrees, HalfArc, bInArc ? TEXT("YES") : TEXT("NO"));
	UE_LOG(LogBlackTideMeleeWeapon, Warning, TEXT("   WielderPos=%s, HitPos=%s"),
		*WielderLocation.ToString(), *HitLocation.ToString());
	UE_LOG(LogBlackTideMeleeWeapon, Warning, TEXT("   WielderForward=%s, HitDirection=%s"),
		*WielderForward.ToString(), *HitDirection.ToString());

	return bInArc;
}

float ABlackTideMeleeWeapon::GetComboMultiplier() const
{
	// Increase damage with combo count
	switch (CurrentComboCount)
	{
		case 1: return 1.0f;    // Normal damage
		case 2: return 1.2f;    // 20% more damage
		case 3: return 1.5f;    // 50% more damage
		default: return 1.0f;
	}
}

UAnimMontage* ABlackTideMeleeWeapon::GetComboMontage(int32 ComboIndex) const
{
	if (ComboIndex <= 0 || ComboIndex > ComboMontages.Num())
	{
		return GetAttackMontage(); // Fall back to default attack montage
	}

	TSoftObjectPtr<UAnimMontage> ComboMontagePtr = ComboMontages[ComboIndex - 1];
	if (ComboMontagePtr.IsNull())
	{
		return GetAttackMontage();
	}

	// Try to get already loaded asset
	UAnimMontage* Montage = ComboMontagePtr.Get();
	if (Montage)
	{
		return Montage;
	}

	// Force load if not already loaded
	Montage = ComboMontagePtr.LoadSynchronous();
	if (Montage)
	{
		UE_LOG(LogBlackTideMeleeWeapon, Log, TEXT("Loaded combo montage %d for %s"), ComboIndex, *WeaponName);
	}

	return Montage;
}

void ABlackTideMeleeWeapon::ResetCombo()
{
	if (CurrentComboCount > 0)
	{
		UE_LOG(LogBlackTideMeleeWeapon, Warning, TEXT("🔄 Combo reset (was at %d/%d)"), 
			CurrentComboCount, MeleeData.MaxComboCount);
	}

	CurrentComboCount = 0;
	LastAttackTime = 0.0f;
}
