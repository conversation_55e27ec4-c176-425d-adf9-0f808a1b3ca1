// Copyright Epic Games, Inc. All Rights Reserved.

#include "BlackTideAnimationComponent.h"
#include "../BlackTideCharacter.h"
#include "../ItemDatabase.h"
#include "Animation/AnimInstance.h"
#include "Components/SkeletalMeshComponent.h"
#include "Engine/World.h"

DEFINE_LOG_CATEGORY_STATIC(LogBlackTideAnimation, Log, All);

UBlackTideAnimationComponent::UBlackTideAnimationComponent()
{
	PrimaryComponentTick.bCanEverTick = false;
	
	// Initialize state
	CurrentMontage = nullptr;
	CurrentAnimationType = EBlackTideAnimationType::None;
	AnimationStartTime = 0.0f;
}

void UBlackTideAnimationComponent::BeginPlay()
{
	Super::BeginPlay();

	// Cache references
	OwnerCharacter = Cast<ABlackTideCharacter>(GetOwner());
	if (OwnerCharacter && OwnerCharacter->GetMesh())
	{
		AnimInstance = OwnerCharacter->GetMesh()->GetAnimInstance();
		
		// Bind to montage events
		if (AnimInstance)
		{
			AnimInstance->OnMontageEnded.AddDynamic(this, &UBlackTideAnimationComponent::OnMontageEnded);
		}
	}

	UE_LOG(LogBlackTideAnimation, Log, TEXT("Animation component initialized for %s"),
		OwnerCharacter ? *OwnerCharacter->GetName() : TEXT("Unknown"));
}

bool UBlackTideAnimationComponent::PlayItemAnimation(const FInventoryItem& Item, EBlackTideAnimationType AnimationType)
{
	if (!AnimInstance || !OwnerCharacter)
	{
		UE_LOG(LogBlackTideAnimation, Warning, TEXT("Cannot play animation - missing AnimInstance or Character"));
		return false;
	}

	UAnimMontage* MontageToPlay = nullptr;
	float PlayRate = 1.0f;

	// Get the appropriate montage based on animation type
	switch (AnimationType)
	{
		case EBlackTideAnimationType::Attack:
			MontageToPlay = GetItemAttackMontage(Item);
			PlayRate = GetItemAttackSpeed(Item);
			break;
			
		case EBlackTideAnimationType::Gather:
			MontageToPlay = GetItemGatherMontage(Item);
			PlayRate = GetItemAttackSpeed(Item); // Use same speed for now
			break;
			
		default:
			UE_LOG(LogBlackTideAnimation, Warning, TEXT("Unsupported animation type: %d"), (int32)AnimationType);
			return false;
	}

	if (!MontageToPlay)
	{
		UE_LOG(LogBlackTideAnimation, Warning, TEXT("No montage found for item %s, animation type %d"), 
			*Item.Name, (int32)AnimationType);
		return false;
	}

	return PlayMontage(MontageToPlay, PlayRate);
}

bool UBlackTideAnimationComponent::PlayMontage(UAnimMontage* Montage, float PlayRate)
{
	if (!Montage || !AnimInstance)
	{
		return false;
	}

	// Stop current animation if playing
	StopCurrentAnimation();

	// Play the new montage
	float Duration = AnimInstance->Montage_Play(Montage, PlayRate);
	if (Duration > 0.0f)
	{
		CurrentMontage = Montage;
		CurrentAnimationType = EBlackTideAnimationType::Attack; // Default for now
		AnimationStartTime = GetWorld()->GetTimeSeconds();

		UE_LOG(LogBlackTideAnimation, Log, TEXT("Playing montage %s with duration %.2f"), 
			*Montage->GetName(), Duration);

		// Broadcast event
		OnAnimationStarted.Broadcast(CurrentAnimationType, Duration);
		return true;
	}

	UE_LOG(LogBlackTideAnimation, Warning, TEXT("Failed to play montage %s"), *Montage->GetName());
	return false;
}

void UBlackTideAnimationComponent::StopCurrentAnimation()
{
	if (CurrentMontage && AnimInstance)
	{
		AnimInstance->Montage_Stop(0.2f, CurrentMontage);
		
		// Broadcast cancellation event
		OnAnimationCancelled.Broadcast(CurrentAnimationType);
		
		CurrentMontage = nullptr;
		CurrentAnimationType = EBlackTideAnimationType::None;
		AnimationStartTime = 0.0f;
		
		UE_LOG(LogBlackTideAnimation, Log, TEXT("Stopped current animation"));
	}
}

bool UBlackTideAnimationComponent::IsPlayingAnimation() const
{
	return CurrentMontage != nullptr && AnimInstance && AnimInstance->Montage_IsPlaying(CurrentMontage);
}

float UBlackTideAnimationComponent::GetAnimationProgress() const
{
	if (!IsPlayingAnimation())
	{
		return 0.0f;
	}

	return AnimInstance->Montage_GetPosition(CurrentMontage) / CurrentMontage->GetPlayLength();
}

UAnimMontage* UBlackTideAnimationComponent::GetItemAttackMontage(const FInventoryItem& Item) const
{
	// Animation is now handled by weapon blueprints, not ItemDatabase
	// This function is kept for backward compatibility but should not be used
	UE_LOG(LogBlackTideAnimation, Warning, TEXT("GetItemAttackMontage called - animations should come from weapon blueprints now"));
	return nullptr;
}

UAnimMontage* UBlackTideAnimationComponent::GetItemGatherMontage(const FInventoryItem& Item) const
{
	// For now, use attack montage for gathering too
	return GetItemAttackMontage(Item);
}

float UBlackTideAnimationComponent::GetItemAttackSpeed(const FInventoryItem& Item) const
{
	// Attack speed is now handled by weapon blueprints, not ItemDatabase
	UE_LOG(LogBlackTideAnimation, Warning, TEXT("GetItemAttackSpeed called - speed should come from weapon blueprints now"));
	return 1.0f;
}

void UBlackTideAnimationComponent::OnMontageEnded(UAnimMontage* Montage, bool bInterrupted)
{
	if (Montage == CurrentMontage)
	{
		EBlackTideAnimationType CompletedType = CurrentAnimationType;
		
		// Clear current state
		CurrentMontage = nullptr;
		CurrentAnimationType = EBlackTideAnimationType::None;
		AnimationStartTime = 0.0f;

		// Broadcast appropriate event
		if (bInterrupted)
		{
			OnAnimationCancelled.Broadcast(CompletedType);
			UE_LOG(LogBlackTideAnimation, Log, TEXT("Animation interrupted"));
		}
		else
		{
			OnAnimationCompleted.Broadcast(CompletedType);
			UE_LOG(LogBlackTideAnimation, Log, TEXT("Animation completed"));
		}
	}
}

FItemAnimationData UBlackTideAnimationComponent::GetAnimationDataForItem(const FInventoryItem& Item) const
{
	FItemAnimationData AnimData;

	// Animation data is now handled by weapon blueprints, not ItemDatabase
	UE_LOG(LogBlackTideAnimation, Warning, TEXT("GetAnimationDataForItem called - data should come from weapon blueprints now"));

	return AnimData;
}

UAnimMontage* UBlackTideAnimationComponent::LoadMontageFromSoftPtr(const TSoftObjectPtr<UAnimMontage>& SoftPtr) const
{
	if (SoftPtr.IsNull())
	{
		return nullptr;
	}

	// Try to get already loaded asset
	UAnimMontage* Montage = SoftPtr.Get();
	if (Montage)
	{
		return Montage;
	}

	// Force load if not already loaded
	Montage = SoftPtr.LoadSynchronous();
	if (Montage)
	{
		UE_LOG(LogBlackTideAnimation, Log, TEXT("Loaded montage: %s"), *Montage->GetName());
	}
	else
	{
		UE_LOG(LogBlackTideAnimation, Warning, TEXT("Failed to load montage from path: %s"), 
			*SoftPtr.ToSoftObjectPath().ToString());
	}

	return Montage;
}
