// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Components/SkeletalMeshComponent.h"
#include "../InventoryItem.h"
#include "../EquipmentTypes.h"
#include "Net/UnrealNetwork.h"
#include "BlackTideEquipmentComponent.generated.h"

class ABlackTideCharacter;
class UInventoryComponent;

/**
 * Structure for equipped item data
 */
USTRUCT(BlueprintType)
struct BLACKTIDE_API FEquippedItem
{
	GENERATED_BODY()

	// The equipped item
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Equipment")
	FInventoryItem Item;

	// Equipment slot
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Equipment")
	EEquipmentSlot Slot = EEquipmentSlot::None;

	// 3D mesh component reference
	UPROPERTY(BlueprintReadOnly, Category = "Equipment")
	TWeakObjectPtr<UStaticMeshComponent> MeshComponent = nullptr;

	// Whether the item is currently drawn/visible
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Equipment")
	bool bIsDrawn = true;

	// Socket name for attachment
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Equipment")
	FName AttachmentSocket = NAME_None;

	FEquippedItem()
	{
		Item = FInventoryItem();
		Slot = EEquipmentSlot::None;
		MeshComponent = nullptr;
		bIsDrawn = true;
		AttachmentSocket = NAME_None;
	}

	bool IsValid() const
	{
		return Item.IsValid() && Slot != EEquipmentSlot::None;
	}

	void Clear()
	{
		Item = FInventoryItem();
		Slot = EEquipmentSlot::None;
		MeshComponent = nullptr;
		bIsDrawn = true;
		AttachmentSocket = NAME_None;
	}
};

/**
 * Delegates for equipment events
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnItemEquipped, EEquipmentSlot, Slot, const FInventoryItem&, Item, bool, bSuccess);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnItemUnequipped, EEquipmentSlot, Slot, const FInventoryItem&, Item, bool, bSuccess);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnEquipmentVisibilityChanged, EEquipmentSlot, Slot, bool, bIsVisible);

/**
 * Equipment management component for BlackTide characters
 * Handles equipping/unequipping items, 3D mesh spawning, and toggle functionality
 */
UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class BLACKTIDE_API UBlackTideEquipmentComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	UBlackTideEquipmentComponent();

protected:
	virtual void BeginPlay() override;
	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
	// Equipment slots (replicated as array since TMap replication is not supported)
	UPROPERTY(Replicated, BlueprintReadOnly, Category = "Equipment")
	TArray<FEquippedItem> EquippedItemsArray;

	// Equipment visibility state (for toggle system)
	UPROPERTY(Replicated, BlueprintReadOnly, Category = "Equipment")
	bool bEquipmentVisible = true;

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Equipment Events")
	FOnItemEquipped OnItemEquipped;

	UPROPERTY(BlueprintAssignable, Category = "Equipment Events")
	FOnItemUnequipped OnItemUnequipped;

	UPROPERTY(BlueprintAssignable, Category = "Equipment Events")
	FOnEquipmentVisibilityChanged OnEquipmentVisibilityChanged;

	// Core equipment functions
	UFUNCTION(BlueprintCallable, Category = "Equipment")
	bool EquipItem(const FInventoryItem& Item, EEquipmentSlot Slot);

	UFUNCTION(BlueprintCallable, Category = "Equipment")
	bool UnequipItem(EEquipmentSlot Slot);

	UFUNCTION(BlueprintCallable, Category = "Equipment")
	bool CanEquipItem(const FInventoryItem& Item, EEquipmentSlot Slot) const;

	UFUNCTION(BlueprintCallable, Category = "Equipment")
	EEquipmentSlot GetBestSlotForItem(const FInventoryItem& Item) const;

	// Query functions
	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Equipment")
	bool IsSlotEquipped(EEquipmentSlot Slot) const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Equipment")
	FInventoryItem GetEquippedItem(EEquipmentSlot Slot) const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Equipment")
	TArray<FInventoryItem> GetAllEquippedItems() const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Equipment")
	bool HasItemEquipped(const FString& ItemID) const;

	// Toggle system
	UFUNCTION(BlueprintCallable, Category = "Equipment")
	void ToggleEquipmentVisibility();

	UFUNCTION(BlueprintCallable, Category = "Equipment")
	void SetEquipmentVisibility(bool bVisible);

	UFUNCTION(BlueprintCallable, Category = "Equipment")
	void ToggleSlotVisibility(EEquipmentSlot Slot);

	// Weapon toggle function - cycles between hand and back
	UFUNCTION(BlueprintCallable, Category = "Equipment")
	void ToggleWeaponPosition();

	// 3D mesh management
	UFUNCTION(BlueprintCallable, Category = "Equipment")
	void UpdateEquipmentMeshes();

	UFUNCTION(BlueprintCallable, Category = "Equipment")
	void SpawnEquipmentMesh(EEquipmentSlot Slot);

	UFUNCTION(BlueprintCallable, Category = "Equipment")
	void SpawnWeaponBlueprint(EEquipmentSlot Slot);

	UFUNCTION(BlueprintCallable, Category = "Equipment")
	void DestroyEquipmentMesh(EEquipmentSlot Slot);

	// Get spawned weapon actor (for advanced weapons)
	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Equipment")
	class ABlackTideWeaponBase* GetEquippedWeapon(EEquipmentSlot Slot) const;

	// Utility functions
	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Equipment")
	bool IsItemEquippable(const FInventoryItem& Item) const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Equipment")
	FName GetSocketForSlot(EEquipmentSlot Slot) const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Equipment")
	UStaticMeshComponent* GetEquipmentMeshComponent(EEquipmentSlot Slot) const;

protected:
	// Internal functions
	void InitializeEquipment();
	bool ValidateEquipmentSlot(EEquipmentSlot Slot) const;
	void UpdateMeshVisibility(EEquipmentSlot Slot);
	UStaticMesh* GetEquipmentMeshFromDatabase(const FInventoryItem& Item) const;
	
	// Server functions
	UFUNCTION(Server, Reliable, WithValidation, Category = "Equipment")
	void ServerEquipItem(const FInventoryItem& Item, EEquipmentSlot Slot);
	bool ServerEquipItem_Validate(const FInventoryItem& Item, EEquipmentSlot Slot);

	UFUNCTION(Server, Reliable, WithValidation, Category = "Equipment")
	void ServerUnequipItem(EEquipmentSlot Slot);
	bool ServerUnequipItem_Validate(EEquipmentSlot Slot);

	UFUNCTION(Server, Reliable, WithValidation, Category = "Equipment")
	void ServerToggleEquipmentVisibility();
	bool ServerToggleEquipmentVisibility_Validate();

	UFUNCTION(Server, Reliable, WithValidation, Category = "Equipment")
	void ServerToggleWeaponPosition();
	bool ServerToggleWeaponPosition_Validate();

	// Replication functions
	UFUNCTION()
	void OnRep_EquippedItemsArray();

	UFUNCTION()
	void OnRep_EquipmentVisible();

private:
	// Local equipment map (not replicated, built from EquippedItemsArray)
	TMap<EEquipmentSlot, FEquippedItem> EquippedItems;

	// Spawned weapon actors (for advanced weapons)
	UPROPERTY()
	TMap<EEquipmentSlot, class ABlackTideWeaponBase*> SpawnedWeapons;

	// Cached references
	UPROPERTY()
	ABlackTideCharacter* OwnerCharacter = nullptr;

	UPROPERTY()
	UInventoryComponent* InventoryComponent = nullptr;

	UPROPERTY()
	USkeletalMeshComponent* CharacterMesh = nullptr;

	// Socket name mappings for equipment slots
	UPROPERTY(EditAnywhere, Category = "Equipment Settings")
	TMap<EEquipmentSlot, FName> SlotSocketMappings;

	// Helper functions for array/map synchronization
	void SyncArrayToMap();
	void SyncMapToArray();

	// Default socket names
	void InitializeDefaultSocketMappings();
};
