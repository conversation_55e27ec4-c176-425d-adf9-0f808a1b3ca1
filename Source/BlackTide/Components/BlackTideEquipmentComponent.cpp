// Copyright Epic Games, Inc. All Rights Reserved.

#include "BlackTideEquipmentComponent.h"
#include "../BlackTideCharacter.h"
#include "../InventoryComponent.h"
#include "../ItemDatabase.h"
#include "../Weapons/BlackTideWeaponBase.h"
#include "Components/SkeletalMeshComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Engine/World.h"
#include "Net/UnrealNetwork.h"
#include "Engine/Engine.h"
#include "Engine/StreamableManager.h"

DEFINE_LOG_CATEGORY_STATIC(LogBlackTideEquipment, Log, All);

UBlackTideEquipmentComponent::UBlackTideEquipmentComponent()
{
	PrimaryComponentTick.bCanEverTick = false;
	SetIsReplicatedByDefault(true);

	// Initialize default values
	bEquipmentVisible = true;
}

void UBlackTideEquipmentComponent::BeginPlay()
{
	Super::BeginPlay();

	// Cache references
	OwnerCharacter = Cast<ABlackTideCharacter>(GetOwner());
	if (OwnerCharacter)
	{
		InventoryComponent = OwnerCharacter->FindComponentByClass<UInventoryComponent>();
		CharacterMesh = OwnerCharacter->GetMesh();
	}

	// Initialize equipment system
	InitializeEquipment();

	UE_LOG(LogBlackTideEquipment, Log, TEXT("Equipment component initialized for %s"),
		OwnerCharacter ? *OwnerCharacter->GetName() : TEXT("Unknown"));
}

void UBlackTideEquipmentComponent::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	// Cleanup all equipment meshes to prevent memory leaks
	for (auto& Pair : EquippedItems)
	{
		DestroyEquipmentMesh(Pair.Key);
	}

	// Clear equipment data
	EquippedItems.Empty();
	EquippedItemsArray.Empty();

	UE_LOG(LogBlackTideEquipment, Log, TEXT("Equipment component cleanup completed"));

	Super::EndPlay(EndPlayReason);
}

void UBlackTideEquipmentComponent::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(UBlackTideEquipmentComponent, EquippedItemsArray);
	DOREPLIFETIME(UBlackTideEquipmentComponent, bEquipmentVisible);
}

bool UBlackTideEquipmentComponent::EquipItem(const FInventoryItem& Item, EEquipmentSlot Slot)
{
	UE_LOG(LogTemp, Error, TEXT("🚀 EQUIP ITEM: EquipItem called for %s in slot %d"), *Item.Name, (int32)Slot);

	// Only server can modify equipment
	if (GetOwnerRole() != ROLE_Authority)
	{
		UE_LOG(LogTemp, Error, TEXT("🌐 EQUIP ITEM: Not authority, calling ServerEquipItem"));
		ServerEquipItem(Item, Slot);
		return false; // Will be handled by server
	}

	UE_LOG(LogTemp, Error, TEXT("✅ EQUIP ITEM: Authority confirmed, proceeding with equip"));

	if (!CanEquipItem(Item, Slot))
	{
		UE_LOG(LogBlackTideEquipment, Warning, TEXT("Cannot equip item %s to slot %d"), *Item.Name, (int32)Slot);
		OnItemEquipped.Broadcast(Slot, Item, false);
		return false;
	}

	// Unequip existing item if slot is occupied
	if (IsSlotEquipped(Slot))
	{
		if (!UnequipItem(Slot))
		{
			UE_LOG(LogBlackTideEquipment, Error, TEXT("Failed to unequip existing item from slot %d"), (int32)Slot);
			OnItemEquipped.Broadcast(Slot, Item, false);
			return false;
		}
	}

	// Create equipped item entry
	FEquippedItem EquippedItem;
	EquippedItem.Item = Item;
	EquippedItem.Slot = Slot;
	EquippedItem.bIsDrawn = bEquipmentVisible;

	// Use PreferredSocket from ItemDatabase if available, otherwise use default slot mapping
	FItemTableRow* ItemDef = UItemDatabase::GetItemDefinition(Item.ItemID);
	if (ItemDef && ItemDef->PreferredSocket != NAME_None)
	{
		EquippedItem.AttachmentSocket = ItemDef->PreferredSocket;
		UE_LOG(LogBlackTideEquipment, Warning, TEXT("🎯 Using preferred socket %s for item %s"),
			*ItemDef->PreferredSocket.ToString(), *Item.Name);
	}
	else
	{
		EquippedItem.AttachmentSocket = GetSocketForSlot(Slot);
		UE_LOG(LogBlackTideEquipment, Warning, TEXT("🔧 Using default socket %s for slot %d (%s)"),
			*EquippedItem.AttachmentSocket.ToString(), (int32)Slot, *Item.Name);
	}

	// NOTE: Don't remove from inventory here - InventoryComponent handles that
	// This prevents data corruption when called from InventoryComponent::EquipTool()
	UE_LOG(LogTemp, Warning, TEXT("🔧 EQUIP ITEM: Skipping inventory removal (handled by caller)"));

	// Add to equipped items
	EquippedItems.Add(Slot, EquippedItem);
	SyncMapToArray(); // Sync to replicated array

	// Spawn 3D mesh
	UE_LOG(LogTemp, Warning, TEXT("🔧 EQUIPMENT: About to spawn mesh for %s in slot %d"), *Item.Name, (int32)Slot);
	SpawnEquipmentMesh(Slot);

	UE_LOG(LogBlackTideEquipment, Log, TEXT("Equipped item %s to slot %d"), *Item.Name, (int32)Slot);
	UE_LOG(LogTemp, Warning, TEXT("✅ EQUIPMENT: Equipped item %s to slot %d"), *Item.Name, (int32)Slot);
	OnItemEquipped.Broadcast(Slot, Item, true);

	return true;
}

bool UBlackTideEquipmentComponent::UnequipItem(EEquipmentSlot Slot)
{
	// Only server can modify equipment
	if (GetOwnerRole() != ROLE_Authority)
	{
		ServerUnequipItem(Slot);
		return false; // Will be handled by server
	}

	if (!IsSlotEquipped(Slot))
	{
		UE_LOG(LogBlackTideEquipment, Warning, TEXT("No item equipped in slot %d"), (int32)Slot);
		return false;
	}

	FEquippedItem* EquippedItem = EquippedItems.Find(Slot);
	if (!EquippedItem)
	{
		return false;
	}

	FInventoryItem ItemToUnequip = EquippedItem->Item;

	// NOTE: Don't return to inventory here - InventoryComponent handles that
	// This prevents duplication when called from InventoryComponent::UnequipTool()
	UE_LOG(LogTemp, Warning, TEXT("🔧 UNEQUIP ITEM: Skipping inventory return (handled by caller)"));

	// Destroy 3D mesh
	DestroyEquipmentMesh(Slot);

	// Remove from equipped items
	EquippedItems.Remove(Slot);
	SyncMapToArray(); // Sync to replicated array

	UE_LOG(LogBlackTideEquipment, Log, TEXT("Unequipped item %s from slot %d"), *ItemToUnequip.Name, (int32)Slot);
	OnItemUnequipped.Broadcast(Slot, ItemToUnequip, true);

	return true;
}

bool UBlackTideEquipmentComponent::CanEquipItem(const FInventoryItem& Item, EEquipmentSlot Slot) const
{
	if (!Item.IsValid() || !ValidateEquipmentSlot(Slot))
	{
		return false;
	}

	// Check if item is equippable
	if (!IsItemEquippable(Item))
	{
		return false;
	}

	// Check slot compatibility based on item type
	switch (Item.ItemType)
	{
		case EItemType::Weapon:
			return Slot == EEquipmentSlot::MainHand || Slot == EEquipmentSlot::OffHand;
		
		case EItemType::Tool:
			return Slot == EEquipmentSlot::MainHand || Slot == EEquipmentSlot::Utility;
		
		case EItemType::Armor:
			// Armor can go in multiple slots - this would be determined by ItemDatabase properties
			return Slot == EEquipmentSlot::Head || Slot == EEquipmentSlot::Chest || 
				   Slot == EEquipmentSlot::Legs || Slot == EEquipmentSlot::Feet;
		
		default:
			return false;
	}
}

EEquipmentSlot UBlackTideEquipmentComponent::GetBestSlotForItem(const FInventoryItem& Item) const
{
	if (!IsItemEquippable(Item))
	{
		return EEquipmentSlot::None;
	}

	// Check if item has a preferred slot defined in ItemDatabase
	FItemTableRow* ItemDef = UItemDatabase::GetItemDefinition(Item.ItemID);
	if (ItemDef && ItemDef->PreferredSlot != EEquipmentSlot::None)
	{
		UE_LOG(LogBlackTideEquipment, Log, TEXT("Using preferred slot %d for item %s"),
			(int32)ItemDef->PreferredSlot, *Item.Name);
		return ItemDef->PreferredSlot;
	}

	// Fallback to default slot based on item type
	switch (Item.ItemType)
	{
		case EItemType::Weapon:
			return EEquipmentSlot::MainHand;

		case EItemType::Tool:
			return EEquipmentSlot::MainHand;

		case EItemType::Armor:
			// Default to chest for armor if no preferred slot specified
			return EEquipmentSlot::Chest;

		default:
			return EEquipmentSlot::None;
	}
}

bool UBlackTideEquipmentComponent::IsSlotEquipped(EEquipmentSlot Slot) const
{
	const FEquippedItem* EquippedItem = EquippedItems.Find(Slot);
	return EquippedItem && EquippedItem->IsValid();
}

FInventoryItem UBlackTideEquipmentComponent::GetEquippedItem(EEquipmentSlot Slot) const
{
	const FEquippedItem* EquippedItem = EquippedItems.Find(Slot);
	return EquippedItem ? EquippedItem->Item : FInventoryItem();
}

TArray<FInventoryItem> UBlackTideEquipmentComponent::GetAllEquippedItems() const
{
	TArray<FInventoryItem> Items;
	for (const auto& Pair : EquippedItems)
	{
		if (Pair.Value.IsValid())
		{
			Items.Add(Pair.Value.Item);
		}
	}
	return Items;
}

bool UBlackTideEquipmentComponent::HasItemEquipped(const FString& ItemID) const
{
	for (const auto& Pair : EquippedItems)
	{
		if (Pair.Value.IsValid() && Pair.Value.Item.ItemID == ItemID)
		{
			return true;
		}
	}
	return false;
}

void UBlackTideEquipmentComponent::ToggleEquipmentVisibility()
{
	UE_LOG(LogBlackTideEquipment, Warning, TEXT("🔄 ToggleEquipmentVisibility called! Current visibility: %s"),
		bEquipmentVisible ? TEXT("VISIBLE") : TEXT("HIDDEN"));

	// Only server can modify visibility
	if (GetOwnerRole() != ROLE_Authority)
	{
		UE_LOG(LogBlackTideEquipment, Warning, TEXT("📡 Client calling server RPC for equipment visibility toggle"));
		ServerToggleEquipmentVisibility();
		return;
	}

	UE_LOG(LogBlackTideEquipment, Warning, TEXT("🖥️ Server executing visibility toggle"));
	SetEquipmentVisibility(!bEquipmentVisible);
}

void UBlackTideEquipmentComponent::SetEquipmentVisibility(bool bVisible)
{
	UE_LOG(LogBlackTideEquipment, Warning, TEXT("🎯 SetEquipmentVisibility called: %s → %s"),
		bEquipmentVisible ? TEXT("VISIBLE") : TEXT("HIDDEN"),
		bVisible ? TEXT("VISIBLE") : TEXT("HIDDEN"));

	if (bEquipmentVisible == bVisible)
	{
		UE_LOG(LogBlackTideEquipment, Warning, TEXT("⚠️ Visibility already set to %s, no change needed"),
			bVisible ? TEXT("VISIBLE") : TEXT("HIDDEN"));
		return;
	}

	bEquipmentVisible = bVisible;

	UE_LOG(LogBlackTideEquipment, Warning, TEXT("🔧 Updating visibility for %d equipped items"), EquippedItems.Num());

	// Update all equipped item visibility
	for (auto& Pair : EquippedItems)
	{
		UE_LOG(LogBlackTideEquipment, Warning, TEXT("  📦 Updating slot %d (%s)"),
			(int32)Pair.Key, *Pair.Value.Item.Name);
		Pair.Value.bIsDrawn = bVisible;
		UpdateMeshVisibility(Pair.Key);
	}

	UE_LOG(LogBlackTideEquipment, Warning, TEXT("✅ Equipment visibility set to: %s"), bVisible ? TEXT("Visible") : TEXT("Hidden"));
	OnEquipmentVisibilityChanged.Broadcast(EEquipmentSlot::None, bVisible);
}

void UBlackTideEquipmentComponent::ToggleSlotVisibility(EEquipmentSlot Slot)
{
	FEquippedItem* EquippedItem = EquippedItems.Find(Slot);
	if (!EquippedItem || !EquippedItem->IsValid())
	{
		return;
	}

	EquippedItem->bIsDrawn = !EquippedItem->bIsDrawn;
	UpdateMeshVisibility(Slot);

	UE_LOG(LogBlackTideEquipment, Log, TEXT("Slot %d visibility toggled to: %s"),
		(int32)Slot, EquippedItem->bIsDrawn ? TEXT("Visible") : TEXT("Hidden"));
	OnEquipmentVisibilityChanged.Broadcast(Slot, EquippedItem->bIsDrawn);
}

void UBlackTideEquipmentComponent::ToggleWeaponPosition()
{
	UE_LOG(LogBlackTideEquipment, Warning, TEXT("🔄 ToggleWeaponPosition called!"));

	// Only server can modify equipment
	if (GetOwnerRole() != ROLE_Authority)
	{
		UE_LOG(LogBlackTideEquipment, Warning, TEXT("📡 Client calling server RPC for weapon position toggle"));
		ServerToggleWeaponPosition();
		return;
	}

	UE_LOG(LogBlackTideEquipment, Warning, TEXT("🖥️ Server executing weapon position toggle"));

	// Find equipped weapon/tool in MainHand slot
	FEquippedItem* MainHandItem = EquippedItems.Find(EEquipmentSlot::MainHand);
	if (!MainHandItem || !MainHandItem->IsValid())
	{
		UE_LOG(LogBlackTideEquipment, Warning, TEXT("⚠️ No weapon equipped in MainHand slot"));
		return;
	}

	// Toggle between hand_r_weapon and back_weapon sockets
	FName CurrentSocket = MainHandItem->AttachmentSocket;
	FName NewSocket;

	if (CurrentSocket == TEXT("hand_r_weapon"))
	{
		NewSocket = TEXT("back_weapon");
		UE_LOG(LogBlackTideEquipment, Warning, TEXT("🔄 Moving weapon from hand to back"));
	}
	else
	{
		NewSocket = TEXT("hand_r_weapon");
		UE_LOG(LogBlackTideEquipment, Warning, TEXT("🔄 Moving weapon from back to hand"));
	}

	// Update the socket and respawn the mesh
	MainHandItem->AttachmentSocket = NewSocket;
	SpawnEquipmentMesh(EEquipmentSlot::MainHand);

	UE_LOG(LogBlackTideEquipment, Warning, TEXT("✅ Weapon position toggled: %s → %s"),
		*CurrentSocket.ToString(), *NewSocket.ToString());
}

void UBlackTideEquipmentComponent::UpdateEquipmentMeshes()
{
	for (const auto& Pair : EquippedItems)
	{
		if (Pair.Value.IsValid())
		{
			SpawnEquipmentMesh(Pair.Key);
		}
	}
}

void UBlackTideEquipmentComponent::SpawnEquipmentMesh(EEquipmentSlot Slot)
{
	UE_LOG(LogTemp, Warning, TEXT("🚀 SPAWN MESH: SpawnEquipmentMesh called for slot %d"), (int32)Slot);

	FEquippedItem* EquippedItem = EquippedItems.Find(Slot);
	if (!EquippedItem)
	{
		UE_LOG(LogTemp, Error, TEXT("❌ SPAWN MESH: No equipped item found for slot %d"), (int32)Slot);
		return;
	}

	if (!EquippedItem->IsValid())
	{
		UE_LOG(LogTemp, Error, TEXT("❌ SPAWN MESH: Equipped item is invalid for slot %d"), (int32)Slot);
		return;
	}

	if (!CharacterMesh)
	{
		UE_LOG(LogTemp, Error, TEXT("❌ SPAWN MESH: CharacterMesh is null!"));
		return;
	}

	UE_LOG(LogTemp, Warning, TEXT("✅ SPAWN MESH: All checks passed for %s"), *EquippedItem->Item.Name);

	// Check if this item uses weapon blueprint or static mesh
	FItemTableRow* ItemDef = UItemDatabase::GetItemDefinition(EquippedItem->Item.ItemID);
	if (!ItemDef)
	{
		UE_LOG(LogBlackTideEquipment, Warning, TEXT("No item definition found for %s"), *EquippedItem->Item.ItemID);
		return;
	}

	// If item has weapon blueprint, use that instead of static mesh
	if (!ItemDef->WeaponBlueprint.IsNull())
	{
		UE_LOG(LogTemp, Warning, TEXT("🗡️ Item %s uses weapon blueprint, spawning weapon actor"), *EquippedItem->Item.Name);
		SpawnWeaponBlueprint(Slot);
		return;
	}

	// Continue with static mesh spawning for simple equipment
	UE_LOG(LogTemp, Warning, TEXT("🔍 SPAWN MESH: Getting static mesh from database for %s"), *EquippedItem->Item.Name);
	UStaticMesh* EquipmentMesh = GetEquipmentMeshFromDatabase(EquippedItem->Item);
	if (!EquipmentMesh)
	{
		UE_LOG(LogBlackTideEquipment, Warning, TEXT("No mesh found for item %s"), *EquippedItem->Item.Name);
		UE_LOG(LogTemp, Error, TEXT("❌ SPAWN MESH: No mesh found for item %s"), *EquippedItem->Item.Name);
		return;
	}
	UE_LOG(LogTemp, Warning, TEXT("✅ SPAWN MESH: Found mesh for %s"), *EquippedItem->Item.Name);

	// Destroy existing mesh if any
	DestroyEquipmentMesh(Slot);

	// Create new mesh component
	UStaticMeshComponent* MeshComponent = NewObject<UStaticMeshComponent>(OwnerCharacter);
	MeshComponent->SetStaticMesh(EquipmentMesh);

	// Setup collision for weapon hits
	MeshComponent->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
	MeshComponent->SetCollisionObjectType(ECC_WorldDynamic);
	MeshComponent->SetCollisionResponseToAllChannels(ECR_Ignore);
	MeshComponent->SetCollisionResponseToChannel(ECC_WorldStatic, ECR_Block);    // Hit environment
	MeshComponent->SetCollisionResponseToChannel(ECC_WorldDynamic, ECR_Block);   // Hit objects
	MeshComponent->SetCollisionResponseToChannel(ECC_Pawn, ECR_Block);           // Hit characters

	// Disable collision by default (enable during attack)
	MeshComponent->SetCollisionEnabled(ECollisionEnabled::NoCollision);

	MeshComponent->SetCastShadow(true);

	// CRITICAL: Register the component with the world so it can be rendered
	MeshComponent->RegisterComponent();
	UE_LOG(LogTemp, Warning, TEXT("🔧 MESH COMPONENT: Registered component with collision setup"));

	// Attach to character mesh
	FName SocketName = EquippedItem->AttachmentSocket;
	UE_LOG(LogBlackTideEquipment, Warning, TEXT("🔧 Attempting to attach %s to socket: %s"),
		*EquippedItem->Item.Name, *SocketName.ToString());

	if (SocketName != NAME_None && CharacterMesh->DoesSocketExist(SocketName))
	{
		MeshComponent->AttachToComponent(CharacterMesh,
			FAttachmentTransformRules::SnapToTargetIncludingScale, SocketName);
		UE_LOG(LogBlackTideEquipment, Warning, TEXT("✅ Successfully attached %s to socket %s"),
			*EquippedItem->Item.Name, *SocketName.ToString());

		// DEBUG: Show mesh component properties after attachment
		FVector WorldLocation = MeshComponent->GetComponentLocation();
		FVector RelativeLocation = MeshComponent->GetRelativeLocation();
		FRotator RelativeRotation = MeshComponent->GetRelativeRotation();
		FVector MeshScale = MeshComponent->GetComponentScale();
		bool bIsVisible = MeshComponent->IsVisible();
		bool bHiddenInGame = MeshComponent->bHiddenInGame;

		UE_LOG(LogTemp, Error, TEXT("🔍 MESH DEBUG: World Location: %s"), *WorldLocation.ToString());
		UE_LOG(LogTemp, Error, TEXT("🔍 MESH DEBUG: Relative Location: %s"), *RelativeLocation.ToString());
		UE_LOG(LogTemp, Error, TEXT("🔍 MESH DEBUG: Relative Rotation: %s"), *RelativeRotation.ToString());
		UE_LOG(LogTemp, Error, TEXT("🔍 MESH DEBUG: Scale: %s"), *MeshScale.ToString());
		UE_LOG(LogTemp, Error, TEXT("🔍 MESH DEBUG: IsVisible: %s"), bIsVisible ? TEXT("TRUE") : TEXT("FALSE"));
		UE_LOG(LogTemp, Error, TEXT("🔍 MESH DEBUG: HiddenInGame: %s"), bHiddenInGame ? TEXT("TRUE") : TEXT("FALSE"));
		UE_LOG(LogTemp, Error, TEXT("🔍 MESH DEBUG: StaticMesh: %s"), MeshComponent->GetStaticMesh() ? *MeshComponent->GetStaticMesh()->GetName() : TEXT("NULL"));
		UE_LOG(LogTemp, Error, TEXT("🔍 MESH DEBUG: AttachParent: %s"), MeshComponent->GetAttachParent() ? *MeshComponent->GetAttachParent()->GetName() : TEXT("NULL"));
		UE_LOG(LogTemp, Error, TEXT("🔍 MESH DEBUG: AttachSocketName: %s"), *MeshComponent->GetAttachSocketName().ToString());

		// Set proper visibility
		MeshComponent->SetVisibility(true);
		MeshComponent->SetHiddenInGame(false);

		// Set reasonable scale (1.0 for normal size, adjust per weapon type)
		MeshComponent->SetRelativeScale3D(FVector(1.0f, 1.0f, 1.0f));

		UE_LOG(LogTemp, Warning, TEXT("🔧 MESH: Set visibility=true, scale=0.4, collision disabled"));
	}
	else
	{
		UE_LOG(LogBlackTideEquipment, Error, TEXT("❌ Socket %s not found for slot %d! Available sockets:"),
			*SocketName.ToString(), (int32)Slot);

		// List available sockets for debugging
		TArray<FName> SocketNames = CharacterMesh->GetAllSocketNames();
		for (const FName& Socket : SocketNames)
		{
			UE_LOG(LogBlackTideEquipment, Error, TEXT("  - %s"), *Socket.ToString());
		}

		MeshComponent->AttachToComponent(CharacterMesh,
			FAttachmentTransformRules::KeepRelativeTransform);
		UE_LOG(LogBlackTideEquipment, Warning, TEXT("⚠️ Attached %s without socket"), *EquippedItem->Item.Name);
	}

	// Apply EquipmentTransform from ItemDatabase if available
	if (ItemDef && !ItemDef->EquipmentTransform.Equals(FTransform::Identity))
	{
		MeshComponent->SetRelativeTransform(ItemDef->EquipmentTransform);
		UE_LOG(LogBlackTideEquipment, Log, TEXT("Applied custom transform for item %s"),
			*EquippedItem->Item.Name);
	}

	// Update equipped item reference
	EquippedItem->MeshComponent = MeshComponent;

	// Set initial visibility
	UpdateMeshVisibility(Slot);

	UE_LOG(LogBlackTideEquipment, Log, TEXT("Spawned equipment mesh for %s in slot %d"),
		*EquippedItem->Item.Name, (int32)Slot);
}

void UBlackTideEquipmentComponent::SpawnWeaponBlueprint(EEquipmentSlot Slot)
{
	UE_LOG(LogBlackTideEquipment, Warning, TEXT("🗡️ SpawnWeaponBlueprint called for slot %d"), (int32)Slot);

	FEquippedItem* EquippedItem = EquippedItems.Find(Slot);
	if (!EquippedItem || !EquippedItem->IsValid())
	{
		UE_LOG(LogBlackTideEquipment, Warning, TEXT("❌ No valid equipped item for weapon blueprint spawn"));
		return;
	}

	// Get item definition
	FItemTableRow* WeaponItemDef = UItemDatabase::GetItemDefinition(EquippedItem->Item.ItemID);
	if (!WeaponItemDef || WeaponItemDef->WeaponBlueprint.IsNull())
	{
		UE_LOG(LogBlackTideEquipment, Warning, TEXT("❌ No weapon blueprint found for item %s"),
			*EquippedItem->Item.ItemID);
		return;
	}

	// Destroy existing weapon if any
	if (ABlackTideWeaponBase** ExistingWeapon = SpawnedWeapons.Find(Slot))
	{
		if (*ExistingWeapon && IsValid(*ExistingWeapon))
		{
			(*ExistingWeapon)->Destroy();
		}
		SpawnedWeapons.Remove(Slot);
	}

	// Load weapon class
	UClass* WeaponClass = WeaponItemDef->WeaponBlueprint.LoadSynchronous();
	if (!WeaponClass)
	{
		UE_LOG(LogBlackTideEquipment, Warning, TEXT("❌ Failed to load weapon blueprint class for %s"),
			*EquippedItem->Item.ItemID);
		return;
	}

	// Spawn weapon actor
	FActorSpawnParameters SpawnParams;
	SpawnParams.Owner = OwnerCharacter;
	SpawnParams.Instigator = OwnerCharacter;
	SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;

	ABlackTideWeaponBase* WeaponActor = GetWorld()->SpawnActor<ABlackTideWeaponBase>(
		WeaponClass, FVector::ZeroVector, FRotator::ZeroRotator, SpawnParams);

	if (!WeaponActor)
	{
		UE_LOG(LogBlackTideEquipment, Warning, TEXT("❌ Failed to spawn weapon actor for %s"),
			*EquippedItem->Item.ItemID);
		return;
	}

	// Attach weapon to character
	FName SocketName = EquippedItem->AttachmentSocket;
	if (SocketName == NAME_None)
	{
		// Default socket based on slot
		switch (Slot)
		{
			case EEquipmentSlot::MainHand:
				SocketName = TEXT("hand_r_weapon");
				break;
			case EEquipmentSlot::OffHand:
				SocketName = TEXT("hand_l_weapon");
				break;
			default:
				SocketName = TEXT("hand_r_weapon");
				break;
		}
	}

	bool bAttached = WeaponActor->AttachToComponent(CharacterMesh,
		FAttachmentTransformRules::SnapToTargetIncludingScale, SocketName);

	if (bAttached)
	{
		// Store reference to spawned weapon
		SpawnedWeapons.Add(Slot, WeaponActor);

		// Update equipped item to store weapon mesh reference
		EquippedItem->MeshComponent = WeaponActor->WeaponMesh;

		UE_LOG(LogBlackTideEquipment, Warning, TEXT("✅ Successfully spawned and attached weapon %s to socket %s"),
			*WeaponActor->WeaponName, *SocketName.ToString());
	}
	else
	{
		UE_LOG(LogBlackTideEquipment, Warning, TEXT("❌ Failed to attach weapon %s to socket %s"),
			*WeaponActor->WeaponName, *SocketName.ToString());
		WeaponActor->Destroy();
	}
}

ABlackTideWeaponBase* UBlackTideEquipmentComponent::GetEquippedWeapon(EEquipmentSlot Slot) const
{
	if (ABlackTideWeaponBase* const* WeaponPtr = SpawnedWeapons.Find(Slot))
	{
		return *WeaponPtr;
	}
	return nullptr;
}

void UBlackTideEquipmentComponent::DestroyEquipmentMesh(EEquipmentSlot Slot)
{
	// Destroy weapon actor if exists
	if (ABlackTideWeaponBase** WeaponPtr = SpawnedWeapons.Find(Slot))
	{
		if (*WeaponPtr && IsValid(*WeaponPtr))
		{
			(*WeaponPtr)->Destroy();
			UE_LOG(LogBlackTideEquipment, Log, TEXT("Destroyed weapon actor for slot %d"), (int32)Slot);
		}
		SpawnedWeapons.Remove(Slot);
	}

	// Destroy static mesh component if exists
	FEquippedItem* EquippedItem = EquippedItems.Find(Slot);
	if (EquippedItem && EquippedItem->MeshComponent.IsValid())
	{
		EquippedItem->MeshComponent->DestroyComponent();
		EquippedItem->MeshComponent = nullptr;
		UE_LOG(LogBlackTideEquipment, Log, TEXT("Destroyed equipment mesh for slot %d"), (int32)Slot);
	}
}

bool UBlackTideEquipmentComponent::IsItemEquippable(const FInventoryItem& Item) const
{
	// Items are equippable if they are Weapon, Tool, or Armor
	return Item.ItemType == EItemType::Weapon || 
		   Item.ItemType == EItemType::Tool || 
		   Item.ItemType == EItemType::Armor;
}

FName UBlackTideEquipmentComponent::GetSocketForSlot(EEquipmentSlot Slot) const
{
	const FName* SocketName = SlotSocketMappings.Find(Slot);
	return SocketName ? *SocketName : NAME_None;
}

UStaticMeshComponent* UBlackTideEquipmentComponent::GetEquipmentMeshComponent(EEquipmentSlot Slot) const
{
	const FEquippedItem* EquippedItem = EquippedItems.Find(Slot);
	return (EquippedItem && EquippedItem->MeshComponent.IsValid()) ? EquippedItem->MeshComponent.Get() : nullptr;
}

// Internal functions
void UBlackTideEquipmentComponent::InitializeEquipment()
{
	// Initialize default socket mappings
	InitializeDefaultSocketMappings();

	// Clear any existing equipment
	EquippedItems.Empty();
	EquippedItemsArray.Empty();

	UE_LOG(LogBlackTideEquipment, Log, TEXT("Equipment system initialized"));
}

bool UBlackTideEquipmentComponent::ValidateEquipmentSlot(EEquipmentSlot Slot) const
{
	return Slot != EEquipmentSlot::None;
}

void UBlackTideEquipmentComponent::UpdateMeshVisibility(EEquipmentSlot Slot)
{
	FEquippedItem* EquippedItem = EquippedItems.Find(Slot);
	if (!EquippedItem || !EquippedItem->MeshComponent.IsValid())
	{
		return;
	}

	bool bShouldBeVisible = EquippedItem->bIsDrawn && bEquipmentVisible;
	EquippedItem->MeshComponent->SetVisibility(bShouldBeVisible);
}

UStaticMesh* UBlackTideEquipmentComponent::GetEquipmentMeshFromDatabase(const FInventoryItem& Item) const
{
	UE_LOG(LogTemp, Warning, TEXT("🔍 GET MESH: Looking up mesh for ItemID: %s"), *Item.ItemID);

	// Get item definition from ItemDatabase (static method)
	FItemTableRow* ItemDef = UItemDatabase::GetItemDefinition(Item.ItemID);
	if (!ItemDef)
	{
		UE_LOG(LogBlackTideEquipment, Warning, TEXT("Item definition not found for %s"), *Item.ItemID);
		UE_LOG(LogTemp, Error, TEXT("❌ GET MESH: Item definition not found for %s"), *Item.ItemID);
		return nullptr;
	}

	UE_LOG(LogTemp, Warning, TEXT("✅ GET MESH: Found item definition for %s"), *Item.ItemID);

	// DEBUG: Show all mesh-related properties
	UE_LOG(LogTemp, Error, TEXT("🔍 DEBUG MESH: ItemDef->EquippedMesh.IsNull(): %s"), ItemDef->EquippedMesh.IsNull() ? TEXT("TRUE") : TEXT("FALSE"));
	UE_LOG(LogTemp, Error, TEXT("🔍 DEBUG MESH: ItemDef->EquippedMesh.IsValid(): %s"), ItemDef->EquippedMesh.IsValid() ? TEXT("TRUE") : TEXT("FALSE"));
	UE_LOG(LogTemp, Error, TEXT("🔍 DEBUG MESH: ItemDef->EquippedMesh.IsPending(): %s"), ItemDef->EquippedMesh.IsPending() ? TEXT("TRUE") : TEXT("FALSE"));
	UE_LOG(LogTemp, Error, TEXT("🔍 DEBUG MESH: ItemDef->EquippedMesh path: %s"), *ItemDef->EquippedMesh.ToSoftObjectPath().ToString());

	// Check if mesh is already loaded
	UStaticMesh* EquippedMesh = ItemDef->EquippedMesh.Get();
	if (EquippedMesh)
	{
		UE_LOG(LogTemp, Warning, TEXT("✅ GET MESH: Mesh already loaded for %s"), *Item.Name);
		return EquippedMesh;
	}

	// Force load the mesh if it exists but isn't loaded yet
	if (!ItemDef->EquippedMesh.IsNull())
	{
		UE_LOG(LogTemp, Warning, TEXT("🔄 GET MESH: Force loading mesh for %s"), *Item.Name);
		EquippedMesh = ItemDef->EquippedMesh.LoadSynchronous();
		if (EquippedMesh)
		{
			UE_LOG(LogTemp, Warning, TEXT("✅ GET MESH: Successfully force-loaded mesh for %s"), *Item.Name);
			return EquippedMesh;
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("❌ GET MESH: Force LoadSynchronous returned null for %s"), *Item.Name);
			UE_LOG(LogTemp, Error, TEXT("❌ GET MESH: Path was: %s"), *ItemDef->EquippedMesh.ToSoftObjectPath().ToString());
		}
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("❌ GET MESH: EquippedMesh is null for %s"), *Item.Name);
	}

	UE_LOG(LogBlackTideEquipment, Warning, TEXT("No equipped mesh defined for item %s"), *Item.Name);
	UE_LOG(LogTemp, Error, TEXT("❌ GET MESH: No equipped mesh defined for item %s (EquippedMesh is null in ItemDatabase)"), *Item.Name);
	return nullptr;
}

void UBlackTideEquipmentComponent::InitializeDefaultSocketMappings()
{
	SlotSocketMappings.Empty();

	// Default socket mappings - updated to match BlackTide character sockets
	SlotSocketMappings.Add(EEquipmentSlot::MainHand, TEXT("hand_r_weapon"));
	SlotSocketMappings.Add(EEquipmentSlot::OffHand, TEXT("hand_l_weapon"));
	SlotSocketMappings.Add(EEquipmentSlot::Head, TEXT("head"));
	SlotSocketMappings.Add(EEquipmentSlot::Back, TEXT("back_weapon"));
	SlotSocketMappings.Add(EEquipmentSlot::Utility, TEXT("pelvis"));

	UE_LOG(LogBlackTideEquipment, Log, TEXT("Initialized socket mappings: MainHand->hand_r_weapon, Back->back_weapon"));
}

// Server RPC implementations
bool UBlackTideEquipmentComponent::ServerEquipItem_Validate(const FInventoryItem& Item, EEquipmentSlot Slot)
{
	return Item.IsValid() && ValidateEquipmentSlot(Slot);
}

void UBlackTideEquipmentComponent::ServerEquipItem_Implementation(const FInventoryItem& Item, EEquipmentSlot Slot)
{
	EquipItem(Item, Slot);
}

bool UBlackTideEquipmentComponent::ServerUnequipItem_Validate(EEquipmentSlot Slot)
{
	return ValidateEquipmentSlot(Slot);
}

void UBlackTideEquipmentComponent::ServerUnequipItem_Implementation(EEquipmentSlot Slot)
{
	UnequipItem(Slot);
}

bool UBlackTideEquipmentComponent::ServerToggleEquipmentVisibility_Validate()
{
	return true;
}

void UBlackTideEquipmentComponent::ServerToggleEquipmentVisibility_Implementation()
{
	ToggleEquipmentVisibility();
}

// Helper functions for array/map synchronization
void UBlackTideEquipmentComponent::SyncArrayToMap()
{
	EquippedItems.Empty();
	for (const FEquippedItem& Item : EquippedItemsArray)
	{
		if (Item.IsValid())
		{
			EquippedItems.Add(Item.Slot, Item);
		}
	}
}

void UBlackTideEquipmentComponent::SyncMapToArray()
{
	EquippedItemsArray.Empty();
	for (const auto& Pair : EquippedItems)
	{
		if (Pair.Value.IsValid())
		{
			EquippedItemsArray.Add(Pair.Value);
		}
	}
}

// Replication functions
void UBlackTideEquipmentComponent::OnRep_EquippedItemsArray()
{
	SyncArrayToMap();
	UpdateEquipmentMeshes();
	UE_LOG(LogBlackTideEquipment, Log, TEXT("Equipment items replicated"));
}

void UBlackTideEquipmentComponent::OnRep_EquipmentVisible()
{
	SetEquipmentVisibility(bEquipmentVisible);
	UE_LOG(LogBlackTideEquipment, Log, TEXT("Equipment visibility replicated: %s"),
		bEquipmentVisible ? TEXT("Visible") : TEXT("Hidden"));
}

// Server RPC implementations
void UBlackTideEquipmentComponent::ServerToggleWeaponPosition_Implementation()
{
	ToggleWeaponPosition();
}

bool UBlackTideEquipmentComponent::ServerToggleWeaponPosition_Validate()
{
	return true;
}
