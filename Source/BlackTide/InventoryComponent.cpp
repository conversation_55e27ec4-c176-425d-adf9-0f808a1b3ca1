// Copyright Epic Games, Inc. All Rights Reserved.

#include "InventoryComponent.h"
#include "BlackTideCharacter.h"
#include "SurvivalComponent.h"
#include "Components/BlackTideEquipmentComponent.h"
#include "EquipmentTypes.h"
#include "Engine/Engine.h"
#include "Net/UnrealNetwork.h"

DEFINE_LOG_CATEGORY_STATIC(LogInventory, Log, All);

UInventoryComponent::UInventoryComponent()
{
	PrimaryComponentTick.bCanEverTick = false;
	SetIsReplicatedByDefault(true);

	// Default grid size: 8x6 = 48 slots
	GridWidth = 8;
	GridHeight = 6;
	MaxWeight = 100.0f;

	// Initialize cache
	CachedWeight = -1.0f;
	bWeightCacheDirty = true;
	bStackableCacheDirty = true;
}

void UInventoryComponent::BeginPlay()
{
	Super::BeginPlay();

	UE_LOG(LogInventory, Warning, TEXT("InventoryComponent::BeginPlay - Role: %s, Owner: %s"),
		GetOwnerRole() == ROLE_Authority ? TEXT("Server") : TEXT("Client"),
		GetOwner() ? *GetOwner()->GetName() : TEXT("None"));

	// Always initialize on server, and on client if slots are empty
	if (GetOwnerRole() == ROLE_Authority || InventorySlots.Num() == 0)
	{
		InitializeInventory();
	}
	else
	{
		UE_LOG(LogInventory, Warning, TEXT("Client skipping initialization - %d slots already exist"), InventorySlots.Num());
	}
}

void UInventoryComponent::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);
	DOREPLIFETIME(UInventoryComponent, InventorySlots);
	DOREPLIFETIME(UInventoryComponent, ActiveToolSlotIndex);
}

void UInventoryComponent::InitializeInventory()
{
	int32 TotalSlots = GridWidth * GridHeight;

	UE_LOG(LogInventory, Warning, TEXT("InitializeInventory: Creating %d slots (%dx%d) for %s"),
		TotalSlots, GridWidth, GridHeight, GetOwner() ? *GetOwner()->GetName() : TEXT("None"));

	InventorySlots.Empty();
	InventorySlots.Reserve(TotalSlots);

	// Initialize all slots as empty
	for (int32 Y = 0; Y < GridHeight; Y++)
	{
		for (int32 X = 0; X < GridWidth; X++)
		{
			FInventorySlot NewSlot(X, Y);
			InventorySlots.Add(NewSlot);
		}
	}

	// Initialize caches
	InvalidateWeightCache();
	InvalidateStackableCache();

	UE_LOG(LogInventory, Warning, TEXT("InitializeInventory: ✅ Created %d slots successfully"), InventorySlots.Num());
}

void UInventoryComponent::OnRep_InventorySlots()
{
	// Invalidate caches when inventory replicates
	InvalidateWeightCache();
	InvalidateStackableCache();

	// Broadcast inventory changed event
	OnInventoryChanged.Broadcast(-1, FInventoryItem()); // -1 indicates full inventory update
}

bool UInventoryComponent::AddItem(const FInventoryItem& Item, int32& OutSlotIndex)
{
	// Only server can modify inventory
	if (GetOwnerRole() != ROLE_Authority)
	{
		UE_LOG(LogInventory, Warning, TEXT("AddItem called on client - only server can modify inventory"));
		return false;
	}

	if (!Item.IsValid())
	{
		UE_LOG(LogInventory, Warning, TEXT("InventoryComponent: Attempted to add invalid item"));
		return false;
	}

	// Safety check: Ensure inventory is initialized
	if (InventorySlots.Num() == 0)
	{
		UE_LOG(LogInventory, Error, TEXT("AddItem: Inventory not initialized! Forcing initialization..."));
		InitializeInventory();

		if (InventorySlots.Num() == 0)
		{
			UE_LOG(LogInventory, Error, TEXT("AddItem: Failed to initialize inventory!"));
			return false;
		}
	}

	// Try to stack with existing items first
	if (Item.IsStackable() && TryStackItem(Item, OutSlotIndex))
	{
		return true;
	}

	// Find empty slot for new item
	OutSlotIndex = FindEmptySlotForItem(Item);
	if (OutSlotIndex == -1)
	{
		OnInventoryFull.Broadcast(Item, Item.CurrentStackSize);
		return false;
	}

	// Check weight limit
	float NewWeight = GetCurrentWeight() + (Item.Weight * Item.CurrentStackSize);
	if (NewWeight > MaxWeight)
	{
		OnInventoryFull.Broadcast(Item, Item.CurrentStackSize);
		return false;
	}

	// Place the item
	if (PlaceItemAtSlot(Item, OutSlotIndex))
	{
		InvalidateWeightCache();
		InvalidateStackableCache();
		OnItemAdded.Broadcast(OutSlotIndex, Item, Item.CurrentStackSize);
		OnInventoryChanged.Broadcast(OutSlotIndex, Item);

		// Debug: Log all item additions to track where starting items come from
		UE_LOG(LogInventory, Warning, TEXT("🎒 ITEM ADDED: '%s' x%d to slot %d (Owner: %s)"),
			*Item.Name, Item.CurrentStackSize, OutSlotIndex,
			GetOwner() ? *GetOwner()->GetName() : TEXT("None"));

		return true;
	}

	return false;
}

bool UInventoryComponent::AddItemToSlot(const FInventoryItem& Item, int32 SlotIndex)
{
	// Only server can modify inventory
	if (GetOwnerRole() != ROLE_Authority)
	{
		UE_LOG(LogInventory, Warning, TEXT("AddItemToSlot called on client - only server can modify inventory"));
		return false;
	}

	if (!Item.IsValid() || !InventorySlots.IsValidIndex(SlotIndex))
	{
		return false;
	}

	if (!CanPlaceItemAtSlot(Item, SlotIndex))
	{
		return false;
	}

	// Check weight limit
	float NewWeight = GetCurrentWeight() + (Item.Weight * Item.CurrentStackSize);
	if (NewWeight > MaxWeight)
	{
		OnInventoryFull.Broadcast(Item, Item.CurrentStackSize);
		return false;
	}

	// Try to stack if slot has same item
	if (!InventorySlots[SlotIndex].IsEmpty())
	{
		FInventoryItem& ExistingItem = InventorySlots[SlotIndex].Item;
		if (ExistingItem.CanStackWith(Item))
		{
			int32 SpaceAvailable = ExistingItem.MaxStackSize - ExistingItem.CurrentStackSize;
			int32 AmountToAdd = FMath::Min(SpaceAvailable, Item.CurrentStackSize);
			
			ExistingItem.CurrentStackSize += AmountToAdd;
			InvalidateWeightCache();
			OnItemAdded.Broadcast(SlotIndex, Item, AmountToAdd);
			OnInventoryChanged.Broadcast(SlotIndex, ExistingItem);
			
			return AmountToAdd > 0;
		}
		return false; // Can't stack and slot is occupied
	}

	// Place new item
	return PlaceItemAtSlot(Item, SlotIndex);
}

bool UInventoryComponent::RemoveItem(int32 SlotIndex, int32 Amount)
{
	// Only server can modify inventory
	if (GetOwnerRole() != ROLE_Authority)
	{
		UE_LOG(LogInventory, Warning, TEXT("RemoveItem called on client - only server can modify inventory"));
		return false;
	}

	if (!InventorySlots.IsValidIndex(SlotIndex) || InventorySlots[SlotIndex].IsEmpty())
	{
		return false;
	}

	FInventorySlot& Slot = InventorySlots[SlotIndex];
	FInventoryItem& Item = Slot.Item;
	int32 AmountToRemove = (Amount <= 0) ? Item.CurrentStackSize : FMath::Min(Amount, Item.CurrentStackSize);
	
	FInventoryItem RemovedItem = Item;
	RemovedItem.CurrentStackSize = AmountToRemove;
	
	Item.CurrentStackSize -= AmountToRemove;
	
	if (Item.CurrentStackSize <= 0)
	{
		// Remove entire item
		ClearSlot(SlotIndex);
	}
	
	InvalidateWeightCache();
	OnItemRemoved.Broadcast(SlotIndex, RemovedItem, AmountToRemove);
	OnInventoryChanged.Broadcast(SlotIndex, Item.CurrentStackSize > 0 ? Item : FInventoryItem());
	
	UE_LOG(LogTemp, Log, TEXT("InventoryComponent: Removed %s (x%d) from slot %d"), 
		*RemovedItem.Name, AmountToRemove, SlotIndex);
	
	return true;
}

bool UInventoryComponent::MoveItem(int32 FromSlotIndex, int32 ToSlotIndex)
{
	if (!InventorySlots.IsValidIndex(FromSlotIndex) || !InventorySlots.IsValidIndex(ToSlotIndex))
	{
		return false;
	}

	if (InventorySlots[FromSlotIndex].IsEmpty())
	{
		return false;
	}

	FInventoryItem ItemToMove = InventorySlots[FromSlotIndex].Item;
	
	// Check if we can place the item at destination
	if (!CanPlaceItemAtSlot(ItemToMove, ToSlotIndex))
	{
		return false;
	}

	// Remove from source
	ClearSlot(FromSlotIndex);
	
	// Place at destination
	if (PlaceItemAtSlot(ItemToMove, ToSlotIndex))
	{
		OnInventoryChanged.Broadcast(FromSlotIndex, FInventoryItem()); // Source now empty
		OnInventoryChanged.Broadcast(ToSlotIndex, ItemToMove); // Destination has item
		
		UE_LOG(LogTemp, Log, TEXT("InventoryComponent: Moved %s from slot %d to slot %d"), 
			*ItemToMove.Name, FromSlotIndex, ToSlotIndex);
		return true;
	}
	else
	{
		// Failed to place at destination, restore at source
		PlaceItemAtSlot(ItemToMove, FromSlotIndex);
		return false;
	}
}

bool UInventoryComponent::SwapItems(int32 SlotIndexA, int32 SlotIndexB)
{
	if (!InventorySlots.IsValidIndex(SlotIndexA) || !InventorySlots.IsValidIndex(SlotIndexB))
	{
		return false;
	}

	if (SlotIndexA == SlotIndexB)
	{
		return false; // Same slot
	}

	FInventoryItem ItemA = InventorySlots[SlotIndexA].Item;
	FInventoryItem ItemB = InventorySlots[SlotIndexB].Item;

	// Clear both slots
	ClearSlot(SlotIndexA);
	ClearSlot(SlotIndexB);

	// Place items in swapped positions
	bool bSuccessA = ItemB.IsValid() ? PlaceItemAtSlot(ItemB, SlotIndexA) : true;
	bool bSuccessB = ItemA.IsValid() ? PlaceItemAtSlot(ItemA, SlotIndexB) : true;

	if (bSuccessA && bSuccessB)
	{
		OnInventoryChanged.Broadcast(SlotIndexA, ItemB);
		OnInventoryChanged.Broadcast(SlotIndexB, ItemA);
		
		UE_LOG(LogTemp, Log, TEXT("InventoryComponent: Swapped items between slots %d and %d"), 
			SlotIndexA, SlotIndexB);
		return true;
	}
	else
	{
		// Restore original state if swap failed
		if (ItemA.IsValid()) PlaceItemAtSlot(ItemA, SlotIndexA);
		if (ItemB.IsValid()) PlaceItemAtSlot(ItemB, SlotIndexB);
		return false;
	}
}

// Query functions
FInventoryItem UInventoryComponent::GetItemAtSlot(int32 SlotIndex) const
{
	if (!InventorySlots.IsValidIndex(SlotIndex))
	{
		return FInventoryItem(); // Invalid item
	}

	return InventorySlots[SlotIndex].Item;
}

bool UInventoryComponent::IsSlotEmpty(int32 SlotIndex) const
{
	if (!InventorySlots.IsValidIndex(SlotIndex))
	{
		return false;
	}

	return InventorySlots[SlotIndex].IsEmpty();
}

bool UInventoryComponent::CanPlaceItemAtSlot(const FInventoryItem& Item, int32 SlotIndex) const
{
	if (!Item.IsValid() || !InventorySlots.IsValidIndex(SlotIndex))
	{
		return false;
	}

	return HasSpaceForItem(Item, SlotIndex);
}

int32 UInventoryComponent::FindEmptySlotForItem(const FInventoryItem& Item) const
{
	if (!Item.IsValid())
	{
		UE_LOG(LogTemp, Warning, TEXT("FindEmptySlotForItem: Item is invalid"));
		return -1;
	}

	UE_LOG(LogTemp, Warning, TEXT("FindEmptySlotForItem: Searching %d slots for '%s'"),
		InventorySlots.Num(), *Item.Name);

	// Search for any empty slot (all items are 1x1 now)
	for (int32 i = 0; i < InventorySlots.Num(); i++)
	{
		if (InventorySlots[i].IsEmpty())
		{
			UE_LOG(LogTemp, Warning, TEXT("FindEmptySlotForItem: Found empty slot %d for '%s'"), i, *Item.Name);
			return i;
		}
		else
		{
			// Log first few occupied slots for debugging
			if (i < 5)
			{
				UE_LOG(LogTemp, Log, TEXT("FindEmptySlotForItem: Slot %d occupied by '%s'"),
					i, *InventorySlots[i].Item.Name);
			}
		}
	}

	UE_LOG(LogTemp, Warning, TEXT("FindEmptySlotForItem: No empty slot found for '%s'"), *Item.Name);
	return -1; // No empty slot found
}

float UInventoryComponent::GetCurrentWeight() const
{
	if (bWeightCacheDirty)
	{
		CachedWeight = 0.0f;

		for (const FInventorySlot& Slot : InventorySlots)
		{
			if (!Slot.IsEmpty() && Slot.Item.IsValid())
			{
				CachedWeight += Slot.Item.Weight * Slot.Item.CurrentStackSize;
			}
		}

		bWeightCacheDirty = false;
	}

	return CachedWeight;
}

float UInventoryComponent::GetWeightPercent() const
{
	return MaxWeight > 0.0f ? (GetCurrentWeight() / MaxWeight) : 0.0f;
}

bool UInventoryComponent::IsOverweight() const
{
	return GetCurrentWeight() > MaxWeight;
}

// Grid utility functions
int32 UInventoryComponent::GetSlotIndex(int32 GridX, int32 GridY) const
{
	if (!IsValidGridPosition(GridX, GridY))
	{
		return -1;
	}

	return GridY * GridWidth + GridX;
}

void UInventoryComponent::GetGridCoordinates(int32 SlotIndex, int32& OutGridX, int32& OutGridY) const
{
	if (InventorySlots.IsValidIndex(SlotIndex))
	{
		OutGridX = SlotIndex % GridWidth;
		OutGridY = SlotIndex / GridWidth;
	}
	else
	{
		OutGridX = -1;
		OutGridY = -1;
	}
}

bool UInventoryComponent::IsValidGridPosition(int32 GridX, int32 GridY) const
{
	return GridX >= 0 && GridX < GridWidth && GridY >= 0 && GridY < GridHeight;
}

// Stack management
bool UInventoryComponent::TryStackItem(const FInventoryItem& Item, int32& OutSlotIndex)
{
	if (!Item.IsStackable())
	{
		return false;
	}

	// Update stackable cache if dirty
	if (bStackableCacheDirty)
	{
		UpdateStackableCache();
	}

	// Use cached lookup for faster stacking
	if (const TArray<int32>* StackableSlots = StackableItemSlots.Find(Item.ItemID))
	{
		for (int32 SlotIndex : *StackableSlots)
		{
			if (!InventorySlots.IsValidIndex(SlotIndex))
				continue;

			FInventorySlot& Slot = InventorySlots[SlotIndex];
			if (!Slot.IsEmpty() && Slot.Item.IsValid() && Slot.Item.CanStackWith(Item))
			{
				int32 SpaceAvailable = Slot.Item.MaxStackSize - Slot.Item.CurrentStackSize;
				int32 AmountToAdd = FMath::Min(SpaceAvailable, Item.CurrentStackSize);

				if (AmountToAdd > 0)
				{
					Slot.Item.CurrentStackSize += AmountToAdd;
					OutSlotIndex = SlotIndex;

					InvalidateWeightCache();
					OnItemAdded.Broadcast(SlotIndex, Item, AmountToAdd);
					OnInventoryChanged.Broadcast(SlotIndex, Slot.Item);
					return true;
				}
			}
		}
	}

	return false;
}

FInventoryItem UInventoryComponent::SplitStack(int32 SlotIndex, int32 Amount)
{
	if (!InventorySlots.IsValidIndex(SlotIndex) || Amount <= 0)
	{
		return FInventoryItem(); // Invalid
	}

	FInventorySlot& Slot = InventorySlots[SlotIndex];
	FInventoryItem& SourceItem = Slot.Item;

	if (!SourceItem.IsValid() || !SourceItem.IsStackable() || Amount >= SourceItem.CurrentStackSize)
	{
		return FInventoryItem(); // Can't split
	}

	// Create split item
	FInventoryItem SplitItem = SourceItem;
	SplitItem.CurrentStackSize = Amount;
	SplitItem.UniqueID = FGuid::NewGuid(); // New unique ID

	// Reduce source stack
	SourceItem.CurrentStackSize -= Amount;

	InvalidateWeightCache();
	OnInventoryChanged.Broadcast(SlotIndex, SourceItem);

	UE_LOG(LogTemp, Log, TEXT("InventoryComponent: Split %d %s from slot %d"),
		Amount, *SplitItem.Name, SlotIndex);

	return SplitItem;
}

// Server functions
void UInventoryComponent::ServerAddItem_Implementation(const FInventoryItem& Item)
{
	int32 SlotIndex;
	AddItem(Item, SlotIndex);
}

void UInventoryComponent::ServerRemoveItem_Implementation(int32 SlotIndex, int32 Amount)
{
	RemoveItem(SlotIndex, Amount);
}

void UInventoryComponent::ServerMoveItem_Implementation(int32 FromSlotIndex, int32 ToSlotIndex)
{
	MoveItem(FromSlotIndex, ToSlotIndex);
}

// Internal functions
bool UInventoryComponent::PlaceItemAtSlot(const FInventoryItem& Item, int32 SlotIndex)
{
	if (!HasSpaceForItem(Item, SlotIndex))
	{
		return false;
	}

	// Verify slot index is valid
	if (!InventorySlots.IsValidIndex(SlotIndex))
	{
		return false;
	}

	// Place the item in the slot (all items are single-slot)
	InventorySlots[SlotIndex].Item = Item;
	return true;
}

void UInventoryComponent::ClearSlot(int32 SlotIndex)
{
	if (!InventorySlots.IsValidIndex(SlotIndex))
	{
		return;
	}

	// Clear the slot (all items are single-slot)
	InventorySlots[SlotIndex].Item = FInventoryItem();
	InvalidateStackableCache();
}



bool UInventoryComponent::HasSpaceForItem(const FInventoryItem& Item, int32 SlotIndex) const
{
	if (!Item.IsValid() || !InventorySlots.IsValidIndex(SlotIndex))
	{
		return false;
	}

	// For 1x1 items, just check if the slot is empty
	return InventorySlots[SlotIndex].IsEmpty();
}

// Debug functions implementation
void UInventoryComponent::DebugSlotStates() const
{
	UE_LOG(LogTemp, Warning, TEXT("=== INVENTORY SLOT STATES DEBUG ==="));
	UE_LOG(LogTemp, Warning, TEXT("Total Slots: %d (Expected: %d)"), InventorySlots.Num(), GridWidth * GridHeight);

	int32 EmptyCount = 0;
	int32 OccupiedCount = 0;
	int32 InvalidCount = 0;

	for (int32 i = 0; i < InventorySlots.Num(); i++)
	{
		const FInventorySlot& Slot = InventorySlots[i];

		bool bIsEmpty = Slot.IsEmpty();
		bool bItemValid = Slot.Item.IsValid();

		if (bIsEmpty)
		{
			EmptyCount++;
			if (i < 5 || i >= InventorySlots.Num() - 5) // Log first and last few
			{
				UE_LOG(LogTemp, Log, TEXT("Slot %d: EMPTY (ItemValid=%s) at (%d,%d)"),
					i, bItemValid ? TEXT("YES") : TEXT("NO"),
					Slot.GridX, Slot.GridY);
			}
		}
		else if (bItemValid)
		{
			OccupiedCount++;
			UE_LOG(LogTemp, Warning, TEXT("Slot %d: '%s' x%d at (%d,%d)"),
				i, *Slot.Item.Name, Slot.Item.CurrentStackSize, Slot.GridX, Slot.GridY);
		}
		else
		{
			InvalidCount++;
			UE_LOG(LogTemp, Error, TEXT("Slot %d: INVALID STATE (ItemValid=%s) at (%d,%d)"),
				i, bItemValid ? TEXT("YES") : TEXT("NO"), Slot.GridX, Slot.GridY);
		}
	}

	UE_LOG(LogTemp, Warning, TEXT("Summary: %d Empty, %d Occupied, %d Invalid"), EmptyCount, OccupiedCount, InvalidCount);
	UE_LOG(LogTemp, Warning, TEXT("=== END SLOT STATES DEBUG ==="));
}

void UInventoryComponent::VerifySlotIntegrity() const
{
	UE_LOG(LogTemp, Warning, TEXT("=== VERIFYING SLOT INTEGRITY ==="));

	// Check array size
	int32 ExpectedSlots = GridWidth * GridHeight;
	if (InventorySlots.Num() != ExpectedSlots)
	{
		UE_LOG(LogTemp, Error, TEXT("❌ SLOT COUNT MISMATCH: Expected %d, Got %d"), ExpectedSlots, InventorySlots.Num());
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("✅ Slot count correct: %d"), InventorySlots.Num());
	}

	// Check grid coordinates
	for (int32 i = 0; i < InventorySlots.Num(); i++)
	{
		const FInventorySlot& Slot = InventorySlots[i];
		int32 ExpectedX = i % GridWidth;
		int32 ExpectedY = i / GridWidth;

		if (Slot.GridX != ExpectedX || Slot.GridY != ExpectedY)
		{
			UE_LOG(LogTemp, Error, TEXT("❌ GRID COORDINATE MISMATCH Slot %d: Expected (%d,%d), Got (%d,%d)"),
				i, ExpectedX, ExpectedY, Slot.GridX, Slot.GridY);
		}
	}

	UE_LOG(LogTemp, Warning, TEXT("=== INTEGRITY CHECK COMPLETE ==="));
}

void UInventoryComponent::UpdateStackableCache() const
{
	if (!bStackableCacheDirty)
		return;

	// Clear existing cache
	StackableItemSlots.Empty();

	// Build cache of stackable items by ItemID
	for (int32 i = 0; i < InventorySlots.Num(); i++)
	{
		const FInventorySlot& Slot = InventorySlots[i];
		if (!Slot.IsEmpty() && Slot.Item.IsValid() && Slot.Item.IsStackable())
		{
			// Add this slot to the list for this ItemID
			TArray<int32>& SlotsForItem = StackableItemSlots.FindOrAdd(Slot.Item.ItemID);
			SlotsForItem.Add(i);
		}
	}

	bStackableCacheDirty = false;
}

bool UInventoryComponent::ValidateInventoryState() const
{
	bool bIsValid = true;
	int32 ErrorCount = 0;

	UE_LOG(LogInventory, Log, TEXT("=== VALIDATING INVENTORY STATE ==="));

	for (int32 i = 0; i < InventorySlots.Num(); i++)
	{
		const FInventorySlot& Slot = InventorySlots[i];

		// Check for invalid items in slots
		if (!Slot.IsEmpty() && !Slot.Item.IsValid())
		{
			UE_LOG(LogInventory, Error, TEXT("Slot %d: Contains invalid item"), i);
			bIsValid = false;
			ErrorCount++;
		}

		// Check for empty items that claim to be valid
		if (Slot.IsEmpty() && Slot.Item.IsValid())
		{
			UE_LOG(LogInventory, Error, TEXT("Slot %d: Empty slot contains valid item"), i);
			bIsValid = false;
			ErrorCount++;
		}

		// Check stack sizes
		if (!Slot.IsEmpty() && Slot.Item.IsValid())
		{
			if (Slot.Item.CurrentStackSize <= 0)
			{
				UE_LOG(LogInventory, Error, TEXT("Slot %d: Item '%s' has invalid stack size %d"),
					i, *Slot.Item.Name, Slot.Item.CurrentStackSize);
				bIsValid = false;
				ErrorCount++;
			}

			if (Slot.Item.CurrentStackSize > Slot.Item.MaxStackSize)
			{
				UE_LOG(LogInventory, Error, TEXT("Slot %d: Item '%s' exceeds max stack size (%d > %d)"),
					i, *Slot.Item.Name, Slot.Item.CurrentStackSize, Slot.Item.MaxStackSize);
				bIsValid = false;
				ErrorCount++;
			}
		}
	}

	if (bIsValid)
	{
		UE_LOG(LogInventory, Log, TEXT("✅ Inventory state is VALID"));
	}
	else
	{
		UE_LOG(LogInventory, Error, TEXT("❌ Inventory state is INVALID - Found %d errors"), ErrorCount);
	}

	return bIsValid;
}

void UInventoryComponent::FixInventoryInconsistencies()
{
	UE_LOG(LogInventory, Warning, TEXT("=== FIXING INVENTORY INCONSISTENCIES ==="));

	int32 FixedCount = 0;

	for (int32 i = 0; i < InventorySlots.Num(); i++)
	{
		FInventorySlot& Slot = InventorySlots[i];

		// Fix invalid items in slots
		if (!Slot.IsEmpty() && !Slot.Item.IsValid())
		{
			UE_LOG(LogInventory, Warning, TEXT("Fixing slot %d: Clearing invalid item"), i);
			Slot.Item = FInventoryItem();
			FixedCount++;
		}

		// Fix stack sizes
		if (!Slot.IsEmpty() && Slot.Item.IsValid())
		{
			if (Slot.Item.CurrentStackSize <= 0)
			{
				UE_LOG(LogInventory, Warning, TEXT("Fixing slot %d: Setting stack size to 1 for '%s'"),
					i, *Slot.Item.Name);
				Slot.Item.CurrentStackSize = 1;
				FixedCount++;
			}

			if (Slot.Item.CurrentStackSize > Slot.Item.MaxStackSize)
			{
				UE_LOG(LogInventory, Warning, TEXT("Fixing slot %d: Clamping stack size for '%s' (%d -> %d)"),
					i, *Slot.Item.Name, Slot.Item.CurrentStackSize, Slot.Item.MaxStackSize);
				Slot.Item.CurrentStackSize = Slot.Item.MaxStackSize;
				FixedCount++;
			}
		}
	}

	if (FixedCount > 0)
	{
		InvalidateWeightCache();
		InvalidateStackableCache();
		OnInventoryChanged.Broadcast(-1, FInventoryItem());
		UE_LOG(LogInventory, Warning, TEXT("✅ Fixed %d inventory inconsistencies"), FixedCount);
	}
	else
	{
		UE_LOG(LogInventory, Log, TEXT("✅ No inconsistencies found to fix"));
	}
}

void UInventoryComponent::DebugEventBindings() const
{
	UE_LOG(LogInventory, Warning, TEXT("=== INVENTORY EVENT BINDINGS DEBUG ==="));

	// Check if delegates have any bindings
	int32 OnInventoryChangedBindings = OnInventoryChanged.IsBound() ? 1 : 0;
	int32 OnItemAddedBindings = OnItemAdded.IsBound() ? 1 : 0;
	int32 OnItemRemovedBindings = OnItemRemoved.IsBound() ? 1 : 0;
	int32 OnInventoryFullBindings = OnInventoryFull.IsBound() ? 1 : 0;

	UE_LOG(LogInventory, Warning, TEXT("OnInventoryChanged bindings: %d"), OnInventoryChangedBindings);
	UE_LOG(LogInventory, Warning, TEXT("OnItemAdded bindings: %d"), OnItemAddedBindings);
	UE_LOG(LogInventory, Warning, TEXT("OnItemRemoved bindings: %d"), OnItemRemovedBindings);
	UE_LOG(LogInventory, Warning, TEXT("OnInventoryFull bindings: %d"), OnInventoryFullBindings);

	// Test broadcast
	UE_LOG(LogInventory, Warning, TEXT("Testing OnInventoryChanged broadcast..."));
	OnInventoryChanged.Broadcast(-1, FInventoryItem());
	UE_LOG(LogInventory, Warning, TEXT("Broadcast completed"));
}

// ===== EQUIPMENT SYSTEM =====

bool UInventoryComponent::EquipTool(int32 SlotIndex)
{
	// Validate slot index
	if (SlotIndex < 0 || SlotIndex >= InventorySlots.Num())
	{
		UE_LOG(LogInventory, Warning, TEXT("EquipTool: Invalid slot index %d"), SlotIndex);
		return false;
	}

	// Check if slot has a valid item
	const FInventorySlot& Slot = InventorySlots[SlotIndex];
	if (Slot.IsEmpty() || !Slot.Item.IsValid())
	{
		UE_LOG(LogInventory, Warning, TEXT("EquipTool: No item in slot %d"), SlotIndex);
		return false;
	}

	// Check if item is a tool
	if (Slot.Item.ItemType != EItemType::Tool)
	{
		UE_LOG(LogInventory, Warning, TEXT("EquipTool: Item '%s' is not a tool"), *Slot.Item.Name);
		return false;
	}

	// Unequip current tool if any
	if (ActiveToolSlotIndex != -1)
	{
		UE_LOG(LogInventory, Log, TEXT("EquipTool: Unequipping current tool"));
		UnequipTool();
	}

	// Equip new tool
	ActiveToolSlotIndex = SlotIndex;
	UE_LOG(LogInventory, Log, TEXT("EquipTool: Equipped '%s' from slot %d"), *Slot.Item.Name, SlotIndex);

	// IMPORTANT: Also equip in EquipmentComponent to spawn 3D mesh!
	ABlackTideCharacter* OwnerCharacter = Cast<ABlackTideCharacter>(GetOwner());
	if (OwnerCharacter && OwnerCharacter->GetEquipmentComponent())
	{
		UBlackTideEquipmentComponent* EquipmentComponent = OwnerCharacter->GetEquipmentComponent();
		EEquipmentSlot BestSlot = EquipmentComponent->GetBestSlotForItem(Slot.Item);

		if (BestSlot != EEquipmentSlot::None)
		{
			UE_LOG(LogInventory, Warning, TEXT("🔧 EquipTool: Also equipping in EquipmentComponent for 3D mesh"));

			// Make a copy to prevent data corruption
			FInventoryItem ItemCopy = Slot.Item;
			UE_LOG(LogInventory, Warning, TEXT("🔧 EquipTool: Equipping copy - Name: %s, ID: %s"), *ItemCopy.Name, *ItemCopy.ItemID);

			bool bEquipSuccess = EquipmentComponent->EquipItem(ItemCopy, BestSlot);
			if (!bEquipSuccess)
			{
				UE_LOG(LogInventory, Error, TEXT("❌ EquipTool: Failed to equip in EquipmentComponent"));
			}
		}
		else
		{
			UE_LOG(LogInventory, Error, TEXT("❌ EquipTool: No suitable equipment slot found"));
		}
	}
	else
	{
		UE_LOG(LogInventory, Error, TEXT("❌ EquipTool: No EquipmentComponent found"));
	}

	// Broadcast inventory change for UI update
	OnInventoryChanged.Broadcast(SlotIndex, Slot.Item);

	return true;
}

void UInventoryComponent::UnequipTool()
{
	if (ActiveToolSlotIndex == -1)
	{
		UE_LOG(LogInventory, Log, TEXT("UnequipTool: No tool currently equipped"));
		return;
	}

	// Get the equipped tool before unequipping
	FInventoryItem EquippedTool = GetEquippedTool();

	int32 PreviousSlot = ActiveToolSlotIndex;
	ActiveToolSlotIndex = -1;

	UE_LOG(LogInventory, Log, TEXT("UnequipTool: Unequipped tool from slot %d"), PreviousSlot);

	// IMPORTANT: Also unequip from EquipmentComponent to remove 3D mesh!
	if (EquippedTool.IsValid())
	{
		ABlackTideCharacter* OwnerCharacter = Cast<ABlackTideCharacter>(GetOwner());
		if (OwnerCharacter && OwnerCharacter->GetEquipmentComponent())
		{
			UBlackTideEquipmentComponent* EquipmentComponent = OwnerCharacter->GetEquipmentComponent();

			// Find which slot the tool is equipped in
			for (int32 SlotIndex = 0; SlotIndex < (int32)EEquipmentSlot::Utility + 1; SlotIndex++)
			{
				EEquipmentSlot Slot = (EEquipmentSlot)SlotIndex;
				if (EquipmentComponent->IsSlotEquipped(Slot))
				{
					FInventoryItem SlotItem = EquipmentComponent->GetEquippedItem(Slot);
					if (SlotItem.ItemID == EquippedTool.ItemID)
					{
						UE_LOG(LogInventory, Warning, TEXT("🔧 UnequipTool: Also unequipping from EquipmentComponent slot %d"), SlotIndex);
						EquipmentComponent->UnequipItem(Slot);
						break;
					}
				}
			}
		}
	}

	// Broadcast inventory change for UI update
	if (PreviousSlot >= 0 && PreviousSlot < InventorySlots.Num())
	{
		OnInventoryChanged.Broadcast(PreviousSlot, InventorySlots[PreviousSlot].Item);
	}
}

FInventoryItem UInventoryComponent::GetEquippedTool() const
{
	if (ActiveToolSlotIndex == -1 || ActiveToolSlotIndex >= InventorySlots.Num())
	{
		return FInventoryItem(); // Return invalid item
	}

	return InventorySlots[ActiveToolSlotIndex].Item;
}

bool UInventoryComponent::HasToolEquipped() const
{
	return ActiveToolSlotIndex != -1 && ActiveToolSlotIndex < InventorySlots.Num() &&
		   !InventorySlots[ActiveToolSlotIndex].IsEmpty();
}

// Item consumption
bool UInventoryComponent::ConsumeItem(int32 SlotIndex)
{
	// Client calls server
	if (GetOwnerRole() != ROLE_Authority)
	{
		ServerConsumeItem(SlotIndex);
		return true; // Assume success for client
	}

	// Server execution
	if (!InventorySlots.IsValidIndex(SlotIndex))
	{
		UE_LOG(LogInventory, Warning, TEXT("ConsumeItem: Invalid slot index %d"), SlotIndex);
		return false;
	}

	const FInventorySlot& Slot = InventorySlots[SlotIndex];
	if (Slot.IsEmpty() || !Slot.Item.IsValid())
	{
		UE_LOG(LogInventory, Warning, TEXT("ConsumeItem: No item in slot %d"), SlotIndex);
		return false;
	}

	// Check if item is consumable
	if (Slot.Item.ItemType != EItemType::Consumable)
	{
		UE_LOG(LogInventory, Warning, TEXT("ConsumeItem: Item '%s' is not consumable"), *Slot.Item.Name);
		return false;
	}

	// Get character and survival component
	ABlackTideCharacter* Character = Cast<ABlackTideCharacter>(GetOwner());
	if (!Character)
	{
		UE_LOG(LogInventory, Error, TEXT("ConsumeItem: Owner is not a BlackTideCharacter"));
		return false;
	}

	USurvivalComponent* SurvivalComp = Character->GetSurvivalComponent();
	if (!SurvivalComp)
	{
		UE_LOG(LogInventory, Error, TEXT("ConsumeItem: Character has no SurvivalComponent"));
		return false;
	}

	// Apply consumable effects
	const FConsumableEffect& Effect = Slot.Item.ConsumableEffect;

	UE_LOG(LogInventory, Warning, TEXT("ConsumeItem: Item '%s' ConsumableEffect - Health:%.1f, Hunger:%.1f, Thirst:%.1f, Stamina:%.1f"),
		*Slot.Item.Name, Effect.HealthRestore, Effect.HungerRestore, Effect.ThirstRestore, Effect.StaminaRestore);

	if (Effect.HealthRestore > 0.0f)
	{
		// Note: Health restoration would need to be implemented in SurvivalComponent
		UE_LOG(LogInventory, Warning, TEXT("ConsumeItem: Restoring %.1f health"), Effect.HealthRestore);
	}

	if (Effect.HungerRestore > 0.0f)
	{
		SurvivalComp->ModifyHunger(Effect.HungerRestore);
		UE_LOG(LogInventory, Warning, TEXT("ConsumeItem: Restored %.1f hunger"), Effect.HungerRestore);
	}

	if (Effect.ThirstRestore > 0.0f)
	{
		SurvivalComp->ModifyThirst(Effect.ThirstRestore);
		UE_LOG(LogInventory, Warning, TEXT("ConsumeItem: Restored %.1f thirst"), Effect.ThirstRestore);
	}

	if (Effect.StaminaRestore > 0.0f)
	{
		SurvivalComp->ModifyStamina(Effect.StaminaRestore);
		UE_LOG(LogInventory, Warning, TEXT("ConsumeItem: Restored %.1f stamina"), Effect.StaminaRestore);
	}

	// Remove one item from stack after successful consumption
	bool bRemoved = RemoveItem(SlotIndex, 1);

	UE_LOG(LogInventory, Warning, TEXT("ConsumeItem: ✅ Successfully consumed %s"), *Slot.Item.Name);
	return bRemoved;
}

// Server RPC implementations
bool UInventoryComponent::ServerConsumeItem_Validate(int32 SlotIndex)
{
	return SlotIndex >= 0 && SlotIndex < InventorySlots.Num();
}

void UInventoryComponent::ServerConsumeItem_Implementation(int32 SlotIndex)
{
	ConsumeItem(SlotIndex);
}

// Initialization functions
void UInventoryComponent::ForceInitializeInventory()
{
	UE_LOG(LogInventory, Warning, TEXT("ForceInitializeInventory called by Blueprint"));
	InitializeInventory();
}

bool UInventoryComponent::IsInventoryInitialized() const
{
	bool bInitialized = InventorySlots.Num() > 0;
	UE_LOG(LogInventory, Log, TEXT("IsInventoryInitialized: %s (%d slots)"),
		bInitialized ? TEXT("YES") : TEXT("NO"), InventorySlots.Num());
	return bInitialized;
}

// Item counting and removal by ID (for crafting system)
int32 UInventoryComponent::GetItemCount(const FString& ItemID) const
{
	if (ItemID.IsEmpty())
	{
		return 0;
	}

	int32 TotalCount = 0;

	for (const FInventorySlot& Slot : InventorySlots)
	{
		if (!Slot.IsEmpty() && Slot.Item.IsValid() && Slot.Item.ItemID == ItemID)
		{
			TotalCount += Slot.Item.CurrentStackSize;
		}
	}

	return TotalCount;
}

bool UInventoryComponent::RemoveItemByID(const FString& ItemID, int32 Amount)
{
	// Only server can modify inventory
	if (GetOwnerRole() != ROLE_Authority)
	{
		UE_LOG(LogInventory, Warning, TEXT("RemoveItemByID called on client - only server can modify inventory"));
		return false;
	}

	if (ItemID.IsEmpty() || Amount <= 0)
	{
		return false;
	}

	// Check if we have enough items
	int32 AvailableAmount = GetItemCount(ItemID);
	if (AvailableAmount < Amount)
	{
		UE_LOG(LogInventory, Warning, TEXT("RemoveItemByID: Not enough '%s' - need %d, have %d"),
			*ItemID, Amount, AvailableAmount);
		return false;
	}

	// Remove items from stacks, starting from the end
	int32 RemainingToRemove = Amount;

	for (int32 i = InventorySlots.Num() - 1; i >= 0 && RemainingToRemove > 0; i--)
	{
		FInventorySlot& Slot = InventorySlots[i];

		if (!Slot.IsEmpty() && Slot.Item.IsValid() && Slot.Item.ItemID == ItemID)
		{
			int32 AmountToRemoveFromSlot = FMath::Min(RemainingToRemove, Slot.Item.CurrentStackSize);

			// Remove from this slot
			bool bSuccess = RemoveItem(i, AmountToRemoveFromSlot);
			if (bSuccess)
			{
				RemainingToRemove -= AmountToRemoveFromSlot;
				UE_LOG(LogInventory, Log, TEXT("RemoveItemByID: Removed %d '%s' from slot %d"),
					AmountToRemoveFromSlot, *ItemID, i);
			}
		}
	}

	bool bFullyRemoved = (RemainingToRemove == 0);
	if (bFullyRemoved)
	{
		UE_LOG(LogInventory, Log, TEXT("RemoveItemByID: Successfully removed %d '%s'"), Amount, *ItemID);
	}
	else
	{
		UE_LOG(LogInventory, Error, TEXT("RemoveItemByID: Failed to remove all items - %d '%s' remaining"),
			RemainingToRemove, *ItemID);
	}

	return bFullyRemoved;
}

bool UInventoryComponent::TryEquipItem(int32 SlotIndex)
{
	if (SlotIndex < 0 || SlotIndex >= InventorySlots.Num())
	{
		UE_LOG(LogInventory, Warning, TEXT("TryEquipItem: Invalid slot index %d"), SlotIndex);
		return false;
	}

	FInventorySlot& Slot = InventorySlots[SlotIndex];
	if (!Slot.Item.IsValid())
	{
		UE_LOG(LogInventory, Warning, TEXT("TryEquipItem: No item in slot %d"), SlotIndex);
		return false;
	}

	// Get equipment component from owner
	ABlackTideCharacter* OwnerCharacter = Cast<ABlackTideCharacter>(GetOwner());
	if (!OwnerCharacter)
	{
		UE_LOG(LogInventory, Error, TEXT("TryEquipItem: Owner is not a BlackTideCharacter"));
		return false;
	}

	UBlackTideEquipmentComponent* EquipmentComponent = OwnerCharacter->GetEquipmentComponent();
	if (!EquipmentComponent)
	{
		UE_LOG(LogInventory, Error, TEXT("TryEquipItem: No EquipmentComponent found"));
		return false;
	}

	// Check if item is equippable
	if (!EquipmentComponent->IsItemEquippable(Slot.Item))
	{
		UE_LOG(LogInventory, Warning, TEXT("TryEquipItem: Item %s is not equippable"), *Slot.Item.Name);
		return false;
	}

	// Get best slot for item
	EEquipmentSlot BestSlot = EquipmentComponent->GetBestSlotForItem(Slot.Item);
	if (BestSlot == EEquipmentSlot::None)
	{
		UE_LOG(LogInventory, Warning, TEXT("TryEquipItem: No suitable equipment slot for item %s"), *Slot.Item.Name);
		return false;
	}

	// Try to equip the item
	bool bSuccess = EquipmentComponent->EquipItem(Slot.Item, BestSlot);
	if (bSuccess)
	{
		UE_LOG(LogInventory, Log, TEXT("TryEquipItem: Successfully equipped %s to slot %d"), *Slot.Item.Name, (int32)BestSlot);
	}
	else
	{
		UE_LOG(LogInventory, Warning, TEXT("TryEquipItem: Failed to equip %s"), *Slot.Item.Name);
	}

	return bSuccess;
}

bool UInventoryComponent::TryUnequipItem(int32 SlotIndex)
{
	// This function is for unequipping items that are currently equipped
	// For now, we'll implement a simple version that checks if the item in the slot is equipped

	if (SlotIndex < 0 || SlotIndex >= InventorySlots.Num())
	{
		UE_LOG(LogInventory, Warning, TEXT("TryUnequipItem: Invalid slot index %d"), SlotIndex);
		return false;
	}

	FInventorySlot& Slot = InventorySlots[SlotIndex];
	if (!Slot.Item.IsValid())
	{
		UE_LOG(LogInventory, Warning, TEXT("TryUnequipItem: No item in slot %d"), SlotIndex);
		return false;
	}

	// Get equipment component from owner
	ABlackTideCharacter* OwnerCharacter = Cast<ABlackTideCharacter>(GetOwner());
	if (!OwnerCharacter)
	{
		UE_LOG(LogInventory, Error, TEXT("TryUnequipItem: Owner is not a BlackTideCharacter"));
		return false;
	}

	UBlackTideEquipmentComponent* EquipmentComponent = OwnerCharacter->GetEquipmentComponent();
	if (!EquipmentComponent)
	{
		UE_LOG(LogInventory, Error, TEXT("TryUnequipItem: No EquipmentComponent found"));
		return false;
	}

	// Check if this item is currently equipped
	if (!EquipmentComponent->HasItemEquipped(Slot.Item.ItemID))
	{
		UE_LOG(LogInventory, Warning, TEXT("TryUnequipItem: Item %s is not currently equipped"), *Slot.Item.Name);
		return false;
	}

	// Find which slot it's equipped in and unequip it
	TArray<FInventoryItem> EquippedItems = EquipmentComponent->GetAllEquippedItems();
	for (int32 SlotInt = 0; SlotInt < 8; SlotInt++) // 8 equipment slots
	{
		EEquipmentSlot EquipSlot = (EEquipmentSlot)SlotInt;
		FInventoryItem EquippedItem = EquipmentComponent->GetEquippedItem(EquipSlot);
		if (EquippedItem.IsValid() && EquippedItem.ItemID == Slot.Item.ItemID)
		{
			bool bSuccess = EquipmentComponent->UnequipItem(EquipSlot);
			if (bSuccess)
			{
				UE_LOG(LogInventory, Log, TEXT("TryUnequipItem: Successfully unequipped %s from slot %d"), *Slot.Item.Name, SlotInt);
			}
			return bSuccess;
		}
	}

	return false;
}

void UInventoryComponent::HandleRightClickOnSlot(int32 SlotIndex)
{
	UE_LOG(LogInventory, Warning, TEXT("🖱️ Right-click on slot %d"), SlotIndex);

	if (SlotIndex < 0 || SlotIndex >= InventorySlots.Num())
	{
		UE_LOG(LogInventory, Warning, TEXT("HandleRightClickOnSlot: Invalid slot index %d"), SlotIndex);
		return;
	}

	FInventorySlot& Slot = InventorySlots[SlotIndex];
	if (!Slot.Item.IsValid())
	{
		UE_LOG(LogInventory, Warning, TEXT("HandleRightClickOnSlot: No item in slot %d"), SlotIndex);
		return;
	}

	// Get equipment component
	ABlackTideCharacter* OwnerCharacter = Cast<ABlackTideCharacter>(GetOwner());
	if (!OwnerCharacter || !OwnerCharacter->GetEquipmentComponent())
	{
		UE_LOG(LogInventory, Error, TEXT("HandleRightClickOnSlot: No equipment component available"));
		return;
	}

	UBlackTideEquipmentComponent* EquipmentComponent = OwnerCharacter->GetEquipmentComponent();

	// Check if item is currently equipped
	if (EquipmentComponent->HasItemEquipped(Slot.Item.ItemID))
	{
		UE_LOG(LogInventory, Warning, TEXT("🔄 Item %s is equipped, attempting to unequip"), *Slot.Item.Name);
		TryUnequipItem(SlotIndex);
	}
	else if (EquipmentComponent->IsItemEquippable(Slot.Item))
	{
		UE_LOG(LogInventory, Warning, TEXT("⚔️ Item %s is equippable, attempting to equip"), *Slot.Item.Name);
		TryEquipItem(SlotIndex);
	}
	else
	{
		UE_LOG(LogInventory, Warning, TEXT("❌ Item %s is not equippable"), *Slot.Item.Name);
	}
}


