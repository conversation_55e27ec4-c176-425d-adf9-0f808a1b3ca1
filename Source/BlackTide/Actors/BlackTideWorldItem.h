// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/StaticMeshComponent.h"
#include "../InventoryItem.h"
#include "BlackTideWorldItem.generated.h"

/**
 * World item that can be picked up and added to inventory
 * Represents any item from ItemDataTable as a 3D object in the world
 */
UCLASS(BlueprintType, Blueprintable)
class BLACKTIDE_API ABlackTideWorldItem : public AActor
{
	GENERATED_BODY()

public:
	ABlackTideWorldItem();

protected:
	virtual void BeginPlay() override;

	// Components
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	UStaticMeshComponent* ItemMesh;

	// Item data
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
	FInventoryItem ItemData;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
	float LifeTime = 300.0f; // 5 minutes before despawn

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
	bool bCanBePickedUp = true;

public:
	// Functions
	UFUNCTION(BlueprintCallable, Category = "World Item")
	void SetupItem(const FInventoryItem& Item, UStaticMesh* Mesh = nullptr);

	UFUNCTION(BlueprintCallable, Category = "World Item")
	void PickupItem(class ABlackTideCharacter* Character);

	UFUNCTION(BlueprintCallable, Category = "World Item")
	void DespawnItem();

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "World Item")
	FInventoryItem GetItemData() const { return ItemData; }

protected:
	// Timer for despawn
	FTimerHandle DespawnTimer;

	// Overlap event
	UFUNCTION()
	void OnOverlapBegin(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor,
		UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);
};
