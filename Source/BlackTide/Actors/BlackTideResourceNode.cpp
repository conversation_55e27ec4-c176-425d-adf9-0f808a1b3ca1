// Copyright Epic Games, Inc. All Rights Reserved.

#include "BlackTideResourceNode.h"
#include "../Components/BlackTideInteractableComponent.h"
#include "../BlackTideCharacter.h"
#include "../InventoryComponent.h"
#include "../ItemDatabase.h"
#include "Components/StaticMeshComponent.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Net/UnrealNetwork.h"
#include "Components/PrimitiveComponent.h"

DEFINE_LOG_CATEGORY_STATIC(LogBlackTideResource, Log, All);

ABlackTideResourceNode::ABlackTideResourceNode()
{
	PrimaryActorTick.bCanEverTick = false;
	bReplicates = true;

	// Create components
	MeshComponent = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("MeshComponent"));
	RootComponent = MeshComponent;

	// Setup collision for resource nodes to be hit by weapons
	MeshComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
	MeshComponent->SetCollisionObjectType(ECC_WorldStatic);
	MeshComponent->SetCollisionResponseToAllChannels(ECR_Block);
	MeshComponent->SetCollisionResponseToChannel(ECC_Pawn, ECR_Block);
	MeshComponent->SetCollisionResponseToChannel(ECC_WorldDynamic, ECR_Block); // Important for weapon hits!

	UE_LOG(LogBlackTideResource, Warning, TEXT("🔧 RESOURCE NODE: Set collision to WorldStatic with weapon hit support"));

	InteractableComponent = CreateDefaultSubobject<UBlackTideInteractableComponent>(TEXT("InteractableComponent"));

	// Set default values
	CurrentHarvestCount = 0;
	bIsHarvested = false;
	LastHarvestTime = 0.0f;
	OriginalMesh = nullptr;
	OriginalScale = FVector::OneVector;

	// Tree falling defaults
	bEnableTreeFalling = true;
	FallForceMultiplier = 500.0f;
	FallDuration = 3.0f;

	// Log spawning defaults
	bSpawnLogsOnFall = true;
	LogActorClass = nullptr;
	MinLogsToSpawn = 2;
	MaxLogsToSpawn = 4;
}

void ABlackTideResourceNode::BeginPlay()
{
	Super::BeginPlay();

	// Store original mesh and scale
	if (MeshComponent && MeshComponent->GetStaticMesh())
	{
		OriginalMesh = MeshComponent->GetStaticMesh();
		OriginalScale = GetActorScale3D();
	}

	// Bind to interaction events
	if (InteractableComponent)
	{
		InteractableComponent->OnInteractionCompleted.AddDynamic(this, &ABlackTideResourceNode::OnInteractionCompleted);
		
		// Set interaction text based on resource type
		// NOTE: Tool requirements are now set in Blueprint only - don't override here!
		switch (ResourceType)
		{
		case EResourceNodeType::Tree:
			InteractableComponent->InteractionText = TEXT("Chop Tree");
			// Don't override Blueprint tool settings
			// InteractableComponent->bRequiresTool = true;
			// InteractableComponent->RequiredToolID = TEXT("IronAxe");
			break;
		case EResourceNodeType::Rock:
			InteractableComponent->InteractionText = TEXT("Mine Rock");
			// Don't override Blueprint tool settings
			// InteractableComponent->bRequiresTool = true;
			// InteractableComponent->RequiredToolID = TEXT("IronPickaxe");
			break;
		case EResourceNodeType::Plant:
			InteractableComponent->InteractionText = TEXT("Gather Plant");
			break;
		case EResourceNodeType::Bush:
			InteractableComponent->InteractionText = TEXT("Gather from Bush");
			break;
		default:
			InteractableComponent->InteractionText = TEXT("Gather Resource");
			break;
		}
	}

	UE_LOG(LogBlackTideResource, Log, TEXT("ResourceNode %s initialized with %d resource drops"), 
		*GetName(), ResourceDrops.Num());
}

void ABlackTideResourceNode::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(ABlackTideResourceNode, CurrentHarvestCount);
	DOREPLIFETIME(ABlackTideResourceNode, bIsHarvested);
	DOREPLIFETIME(ABlackTideResourceNode, bIsBeingHarvested);
}

void ABlackTideResourceNode::OnInteractionCompleted(ABlackTideCharacter* Character)
{
	if (!Character)
	{
		return;
	}

	// Only process on server
	if (GetLocalRole() != ROLE_Authority)
	{
		return;
	}

	HarvestResource(Character);
}

void ABlackTideResourceNode::HarvestResource(ABlackTideCharacter* HarvesterCharacter)
{
	// Only server can harvest
	if (GetLocalRole() != ROLE_Authority)
	{
		UE_LOG(LogBlackTideResource, Warning, TEXT("HarvestResource called on client - ignoring"));
		return;
	}

	if (!CanBeHarvested() || !HarvesterCharacter)
	{
		UE_LOG(LogBlackTideResource, Warning, TEXT("Cannot harvest resource - conditions not met"));
		return;
	}

	// Race condition protection - check if someone is already harvesting
	if (bIsBeingHarvested)
	{
		UE_LOG(LogBlackTideResource, Warning, TEXT("Resource is already being harvested by another player"));
		return;
	}

	// Mark as being harvested to prevent race conditions
	bIsBeingHarvested = true;

	UInventoryComponent* Inventory = HarvesterCharacter->GetInventoryComponent();
	if (!Inventory)
	{
		bIsBeingHarvested = false; // Reset flag
		UE_LOG(LogBlackTideResource, Warning, TEXT("Harvester has no inventory component"));
		return;
	}

	// Generate resource drops
	TArray<FInventoryItem> DroppedItems = GenerateResourceDrops();

	// Try to add items to inventory
	int32 ItemsAdded = 0;
	for (const FInventoryItem& Item : DroppedItems)
	{
		int32 SlotIndex;
		if (Inventory->AddItem(Item, SlotIndex))
		{
			ItemsAdded++;
			UE_LOG(LogBlackTideResource, Log, TEXT("Added %s x%d to inventory"), 
				*Item.Name, Item.CurrentStackSize);
		}
		else
		{
			UE_LOG(LogBlackTideResource, Warning, TEXT("Failed to add %s to inventory - full?"), *Item.Name);
		}
	}

	// Always reset the harvesting flag
	bIsBeingHarvested = false;

	if (ItemsAdded > 0)
	{
		// Update harvest state
		CurrentHarvestCount++;
		LastHarvestTime = GetWorld()->GetTimeSeconds();

		// Check if resource is depleted
		if (CurrentHarvestCount >= MaxHarvestCount)
		{
			bIsHarvested = true;

			// Start tree falling animation for trees
			if (ResourceType == EResourceNodeType::Tree && bEnableTreeFalling)
			{
				StartTreeFalling(HarvesterCharacter);
			}
			else
			{
				UpdateVisualState();
			}

			if (bRespawns)
			{
				StartRespawnTimer();
			}
			else if (bDestroyOnHarvest)
			{
				Destroy();
				return;
			}
		}

		UE_LOG(LogBlackTideResource, Log, TEXT("Resource harvested by %s. Count: %d/%d"),
			*HarvesterCharacter->GetName(), CurrentHarvestCount, MaxHarvestCount);

		// Broadcast Blueprint event
		OnResourceHarvested(HarvesterCharacter);
	}
}

bool ABlackTideResourceNode::CanBeHarvested() const
{
	return !bIsHarvested && !bIsBeingHarvested && CurrentHarvestCount < MaxHarvestCount;
}

float ABlackTideResourceNode::GetRespawnProgress() const
{
	if (!bIsHarvested || !bRespawns || RespawnTime <= 0.0f)
	{
		return 0.0f;
	}

	float CurrentTime = GetWorld()->GetTimeSeconds();
	float ElapsedTime = CurrentTime - LastHarvestTime;
	return FMath::Clamp(ElapsedTime / RespawnTime, 0.0f, 1.0f);
}

TArray<FInventoryItem> ABlackTideResourceNode::GenerateResourceDrops() const
{
	TArray<FInventoryItem> DroppedItems;

	for (const FResourceDrop& Drop : ResourceDrops)
	{
		// Check drop chance
		if (FMath::RandRange(0.0f, 1.0f) <= Drop.DropChance)
		{
			// Calculate amount
			int32 Amount = FMath::RandRange(Drop.MinAmount, Drop.MaxAmount);
			
			// Create item
			FInventoryItem Item = UItemDatabase::CreateItem(Drop.ItemID, Amount);
			if (Item.IsValid())
			{
				DroppedItems.Add(Item);
			}
		}
	}

	return DroppedItems;
}

void ABlackTideResourceNode::ResetResource()
{
	if (GetLocalRole() != ROLE_Authority)
	{
		return;
	}

	CurrentHarvestCount = 0;
	bIsHarvested = false;
	bIsBeingHarvested = false; // Reset harvesting flag
	LastHarvestTime = 0.0f;

	// Clear respawn timer
	GetWorld()->GetTimerManager().ClearTimer(RespawnTimerHandle);

	// Update visual state
	UpdateVisualState();

	UE_LOG(LogBlackTideResource, Log, TEXT("Resource %s has respawned"), *GetName());

	// Broadcast Blueprint event
	OnResourceRespawned();
}

void ABlackTideResourceNode::OnRep_IsHarvested()
{
	// Update visual state when harvested state changes
	UpdateVisualState();

	UE_LOG(LogBlackTideResource, Log, TEXT("Resource %s harvested state replicated: %s"),
		*GetName(), bIsHarvested ? TEXT("Harvested") : TEXT("Available"));
}

void ABlackTideResourceNode::UpdateVisualState()
{
	if (!MeshComponent)
	{
		return;
	}

	if (bIsHarvested)
	{
		// Switch to harvested appearance
		if (HarvestedMesh)
		{
			MeshComponent->SetStaticMesh(HarvestedMesh);
		}
		SetActorScale3D(HarvestedScale);

		// Disable interaction
		if (InteractableComponent)
		{
			InteractableComponent->bIsInteractable = false;
		}
	}
	else
	{
		// Restore original appearance
		if (OriginalMesh)
		{
			MeshComponent->SetStaticMesh(OriginalMesh);
		}
		SetActorScale3D(OriginalScale);

		// Enable interaction
		if (InteractableComponent)
		{
			InteractableComponent->bIsInteractable = true;
		}
	}
}

void ABlackTideResourceNode::StartRespawnTimer()
{
	if (RespawnTime > 0.0f)
	{
		GetWorld()->GetTimerManager().SetTimer(
			RespawnTimerHandle,
			this,
			&ABlackTideResourceNode::ResetResource,
			RespawnTime,
			false
		);

		UE_LOG(LogBlackTideResource, Log, TEXT("Respawn timer started for %s (%.1f seconds)"),
			*GetName(), RespawnTime);
	}
}

// Blueprint configuration helpers
void ABlackTideResourceNode::SetupAsTree(int32 HarvestCount, float RespawnTimeSeconds)
{
	ResourceType = EResourceNodeType::Tree;
	MaxHarvestCount = HarvestCount;
	RespawnTime = RespawnTimeSeconds;

	if (InteractableComponent)
	{
		InteractableComponent->InteractionText = TEXT("Chop Tree");
		// Don't override Blueprint tool settings
		// InteractableComponent->bRequiresTool = true;
		// InteractableComponent->RequiredToolID = TEXT("IronAxe");
		InteractableComponent->InteractionDuration = 3.0f;
	}

	// Only add default drops if none are configured (preserves Blueprint settings)
	if (ResourceDrops.Num() == 0)
	{
		AddResourceDrop(TEXT("wood"), 3, 5, 1.0f);
		AddResourceDrop(TEXT("sticks"), 2, 4, 0.8f);
		UE_LOG(LogBlackTideResource, Log, TEXT("Added default tree drops for %s"), *GetName());
	}
	else
	{
		UE_LOG(LogBlackTideResource, Log, TEXT("Using existing %d resource drops for %s"), ResourceDrops.Num(), *GetName());
	}

	UE_LOG(LogBlackTideResource, Log, TEXT("Configured %s as Tree"), *GetName());
}

void ABlackTideResourceNode::SetupAsRock(int32 HarvestCount, float RespawnTimeSeconds)
{
	ResourceType = EResourceNodeType::Rock;
	MaxHarvestCount = HarvestCount;
	RespawnTime = RespawnTimeSeconds;

	if (InteractableComponent)
	{
		InteractableComponent->InteractionText = TEXT("Mine Rock");
		// Don't override Blueprint tool settings
		// InteractableComponent->bRequiresTool = true;
		// InteractableComponent->RequiredToolID = TEXT("IronPickaxe");
		InteractableComponent->InteractionDuration = 4.0f;
	}

	// Only add default drops if none are configured (preserves Blueprint settings)
	if (ResourceDrops.Num() == 0)
	{
		AddResourceDrop(TEXT("stone"), 2, 4, 1.0f);
		AddResourceDrop(TEXT("flint"), 1, 2, 0.3f);
		UE_LOG(LogBlackTideResource, Log, TEXT("Added default rock drops for %s"), *GetName());
	}
	else
	{
		UE_LOG(LogBlackTideResource, Log, TEXT("Using existing %d resource drops for %s"), ResourceDrops.Num(), *GetName());
	}

	UE_LOG(LogBlackTideResource, Log, TEXT("Configured %s as Rock"), *GetName());
}

void ABlackTideResourceNode::SetupAsPlant(int32 HarvestCount, float RespawnTimeSeconds)
{
	ResourceType = EResourceNodeType::Plant;
	MaxHarvestCount = HarvestCount;
	RespawnTime = RespawnTimeSeconds;

	if (InteractableComponent)
	{
		InteractableComponent->InteractionText = TEXT("Gather Plant");
		InteractableComponent->bRequiresTool = false;
		InteractableComponent->InteractionDuration = 2.0f;
	}

	// Only add default drops if none are configured (preserves Blueprint settings)
	if (ResourceDrops.Num() == 0)
	{
		AddResourceDrop(TEXT("yucca_root"), 1, 3, 1.0f);
		AddResourceDrop(TEXT("fiber"), 2, 5, 0.7f);
		UE_LOG(LogBlackTideResource, Log, TEXT("Added default plant drops for %s"), *GetName());
	}
	else
	{
		UE_LOG(LogBlackTideResource, Log, TEXT("Using existing %d resource drops for %s"), ResourceDrops.Num(), *GetName());
	}

	UE_LOG(LogBlackTideResource, Log, TEXT("Configured %s as Plant"), *GetName());
}

void ABlackTideResourceNode::SetupAsBush(int32 HarvestCount, float RespawnTimeSeconds)
{
	ResourceType = EResourceNodeType::Bush;
	MaxHarvestCount = HarvestCount;
	RespawnTime = RespawnTimeSeconds;

	if (InteractableComponent)
	{
		InteractableComponent->InteractionText = TEXT("Gather from Bush");
		InteractableComponent->bRequiresTool = false;
		InteractableComponent->InteractionDuration = 2.5f;
	}

	// Only add default drops if none are configured (preserves Blueprint settings)
	if (ResourceDrops.Num() == 0)
	{
		AddResourceDrop(TEXT("berries"), 3, 8, 1.0f);
		AddResourceDrop(TEXT("sticks"), 1, 3, 0.5f);
		UE_LOG(LogBlackTideResource, Log, TEXT("Added default bush drops for %s"), *GetName());
	}
	else
	{
		UE_LOG(LogBlackTideResource, Log, TEXT("Using existing %d resource drops for %s"), ResourceDrops.Num(), *GetName());
	}

	UE_LOG(LogBlackTideResource, Log, TEXT("Configured %s as Bush"), *GetName());
}

void ABlackTideResourceNode::AddResourceDrop(const FString& ItemID, int32 MinAmount, int32 MaxAmount, float DropChance)
{
	FResourceDrop NewDrop;
	NewDrop.ItemID = ItemID;
	NewDrop.MinAmount = MinAmount;
	NewDrop.MaxAmount = MaxAmount;
	NewDrop.DropChance = FMath::Clamp(DropChance, 0.0f, 1.0f);

	ResourceDrops.Add(NewDrop);

	UE_LOG(LogBlackTideResource, Log, TEXT("Added resource drop: %s (%d-%d, %.1f%% chance)"),
		*ItemID, MinAmount, MaxAmount, DropChance * 100.0f);
}

void ABlackTideResourceNode::StartTreeFalling(ABlackTideCharacter* FellerCharacter)
{
	if (!MeshComponent || ResourceType != EResourceNodeType::Tree)
	{
		UE_LOG(LogBlackTideResource, Warning, TEXT("Cannot start tree falling - not a tree or no mesh"));
		UpdateVisualState(); // Fallback to normal visual update
		return;
	}

	UE_LOG(LogBlackTideResource, Warning, TEXT("🌳 Starting simple tree falling animation for %s"), *GetName());

	// Skip physics - just do a simple animated fall
	// Calculate fall direction (away from the character who felled it)
	FVector FallDirection = FVector::ZeroVector;
	if (FellerCharacter)
	{
		FVector ToFeller = (FellerCharacter->GetActorLocation() - GetActorLocation()).GetSafeNormal();
		FallDirection = -ToFeller; // Fall away from the feller
		FallDirection.Z = 0.0f; // Keep it horizontal
		FallDirection = FallDirection.GetSafeNormal();
	}
	else
	{
		// Random fall direction if no feller
		FallDirection = FVector(FMath::RandRange(-1.0f, 1.0f), FMath::RandRange(-1.0f, 1.0f), 0.0f).GetSafeNormal();
	}

	// Store original transform
	FVector OriginalLocation = GetActorLocation();
	FRotator OriginalRotation = GetActorRotation();

	// Calculate final fallen position and rotation
	FVector FinalLocation = OriginalLocation + (FallDirection * 300.0f); // Fall 3 meters away
	FinalLocation.Z = OriginalLocation.Z; // Keep same height, don't go underground

	FRotator FinalRotation = OriginalRotation;
	FinalRotation.Pitch = 90.0f; // Tip over
	FinalRotation.Yaw += FMath::RandRange(-30.0f, 30.0f); // Random variation

	UE_LOG(LogBlackTideResource, Warning, TEXT("🌳 Tree will fall from %s to %s"),
		*OriginalLocation.ToString(), *FinalLocation.ToString());

	// Simple timer-based animation using member variable
	float ElapsedTime = 0.0f;

	GetWorld()->GetTimerManager().SetTimer(TreeFallTimerHandle, [this, OriginalLocation, OriginalRotation, FinalLocation, FinalRotation, ElapsedTime]() mutable
	{
		ElapsedTime += 0.016f; // Update at ~60 FPS (1/60 = 0.016)
		float Alpha = FMath::Clamp(ElapsedTime / FallDuration, 0.0f, 1.0f);

		// Smooth interpolation
		Alpha = FMath::SmoothStep(0.0f, 1.0f, Alpha);

		// Interpolate position and rotation
		FVector CurrentLocation = FMath::Lerp(OriginalLocation, FinalLocation, Alpha);
		FRotator CurrentRotation = FMath::Lerp(OriginalRotation, FinalRotation, Alpha);

		SetActorLocationAndRotation(CurrentLocation, CurrentRotation);

		UE_LOG(LogBlackTideResource, Log, TEXT("🌳 Fall progress: %.1f%%, Location: %s"),
			Alpha * 100.0f, *CurrentLocation.ToString());

		// When animation is complete
		if (Alpha >= 1.0f)
		{
			GetWorld()->GetTimerManager().ClearTimer(TreeFallTimerHandle);

			UE_LOG(LogBlackTideResource, Warning, TEXT("🪵 Tree fall complete! Final location: %s"), *FinalLocation.ToString());

			if (bSpawnLogsOnFall)
			{
				UE_LOG(LogBlackTideResource, Warning, TEXT("🪵 Attempting to spawn logs..."));
				SpawnLogsAtLocation(FinalLocation);
			}
			else
			{
				UE_LOG(LogBlackTideResource, Warning, TEXT("🪵 Log spawning disabled"));
			}

			// Update visual state after falling
			UpdateVisualState();

			UE_LOG(LogBlackTideResource, Warning, TEXT("🪵 Tree fall animation complete"));
		}
	}, 0.016f, true); // Repeat at 60 FPS
}

void ABlackTideResourceNode::SpawnLogsAtLocation(const FVector& Location)
{
	if (!LogActorClass)
	{
		UE_LOG(LogBlackTideResource, Warning, TEXT("No LogActorClass set - cannot spawn logs"));
		return;
	}

	int32 NumLogs = FMath::RandRange(MinLogsToSpawn, MaxLogsToSpawn);
	UE_LOG(LogBlackTideResource, Warning, TEXT("🪵 Spawning %d logs at %s"), NumLogs, *Location.ToString());

	for (int32 i = 0; i < NumLogs; i++)
	{
		// Spawn logs in a small radius around the tree
		FVector SpawnLocation = Location + FVector(
			FMath::RandRange(-100.0f, 100.0f),
			FMath::RandRange(-100.0f, 100.0f),
			50.0f // Slightly above ground
		);

		FRotator SpawnRotation = FRotator(
			FMath::RandRange(-15.0f, 15.0f), // Small pitch variation
			FMath::RandRange(0.0f, 360.0f),  // Random yaw
			FMath::RandRange(-15.0f, 15.0f)  // Small roll variation
		);

		AActor* SpawnedLog = GetWorld()->SpawnActor<AActor>(LogActorClass, SpawnLocation, SpawnRotation);
		if (SpawnedLog)
		{
			// Give logs some physics impulse for realistic scattering
			UStaticMeshComponent* LogMesh = SpawnedLog->FindComponentByClass<UStaticMeshComponent>();
			if (LogMesh)
			{
				LogMesh->SetSimulatePhysics(true);
				FVector RandomImpulse = FVector(
					FMath::RandRange(-200.0f, 200.0f),
					FMath::RandRange(-200.0f, 200.0f),
					FMath::RandRange(100.0f, 300.0f)
				);
				LogMesh->AddImpulse(RandomImpulse);
			}

			UE_LOG(LogBlackTideResource, Log, TEXT("Spawned log %d at %s"), i + 1, *SpawnLocation.ToString());
		}
	}
}
